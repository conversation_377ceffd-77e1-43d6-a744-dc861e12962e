{"key": "novelGuide", "name": "小说创作指南", "desc": "一份全面的小说创作指南，涵盖构思、结构、内容、表现和支撑等多个方面，旨在帮助创作者构建完整的故事体系。", "value": null, "fields": [{"key": "<PERSON><PERSON><PERSON><PERSON>", "name": "构思层", "desc": "故事的起源，确定故事的核心元素。", "value": null, "fields": [{"key": "storyCore", "name": "故事核", "desc": "故事的核心创意，是故事的基础。", "value": null, "fields": []}, {"key": "themeExpression", "name": "主题表达", "desc": "故事想要传达的中心思想。", "value": null, "fields": []}, {"key": "worldviewConstruction", "name": "世界观构建", "desc": "故事发生的时空规则，包括历史、地理、文化等。", "value": null, "fields": []}, {"key": "typePositioning", "name": "类型定位", "desc": "确定小说的类型，如科幻、悬疑、爱情等。", "value": ["科幻", "悬疑", "爱情", "奇幻", "历史", "武侠", "都市"], "fields": []}]}, {"key": "<PERSON><PERSON><PERSON>er", "name": "结构层", "desc": "故事的骨架，决定故事的整体走向。", "value": null, "fields": [{"key": "outline", "name": "大纲", "desc": "故事的整体框架，包括三幕剧、英雄之旅等模型。", "value": ["三幕剧", "英雄之旅"], "fields": []}, {"key": "chapterStrategy", "name": "分章策略", "desc": "章节的划分方式，包括POV运用、时间线设计等。", "value": ["POV运用", "时间线设计"], "fields": []}, {"key": "plotNodes", "name": "情节节点", "desc": "故事中的关键情节，包括激励事件、高潮、结局等。", "value": ["激励事件", "高潮", "结局"], "fields": []}]}, {"key": "contentLayer", "name": "内容层", "desc": "故事的核心元素，填充故事的血肉。", "value": null, "fields": [{"key": "characterSystem", "name": "人物系统", "desc": "人物的设定，包括主角、反派、配角等。", "value": null, "fields": [{"key": "protagonist", "name": "主角", "desc": "故事的核心人物，推动故事发展。", "value": null, "fields": []}, {"key": "antagonist", "name": "反派", "desc": "与主角对立的人物，制造冲突。", "value": null, "fields": []}, {"key": "supportingCharacters", "name": "配角", "desc": "辅助主角或反派的人物，丰富故事。", "value": null, "fields": []}, {"key": "characterArcDesign", "name": "人物弧光设计", "desc": "人物的成长历程。", "value": null, "fields": []}]}, {"key": "sceneEngineering", "name": "场景工程", "desc": "场景的构建，包括关键场景、过渡场景等。", "value": null, "fields": [{"key": "keyScenes", "name": "关键场景", "desc": "展示转折的场景。", "value": null, "fields": []}, {"key": "transitionScenes", "name": "过渡场景", "desc": "调节节奏的场景。", "value": null, "fields": []}]}, {"key": "detailSystem", "name": "细节系统", "desc": "故事中的细节，包括伏笔、照应、象征元素等。", "value": null, "fields": [{"key": "foreshadowingAndEchoes", "name": "伏笔与照应", "desc": "为故事增添深度和趣味。", "value": null, "fields": []}, {"key": "symbolicElements", "name": "象征元素", "desc": "赋予故事更深层次的含义。", "value": null, "fields": []}]}]}, {"key": "expression<PERSON>ayer", "name": "表现层", "desc": "故事的呈现方式，影响读者的阅读体验。", "value": null, "fields": [{"key": "narrativePerspectiveChoice", "name": "叙事视角选择", "desc": "选择合适的叙事视角，如第一人称、第三人称等。", "value": ["第一人称", "第三人称", "全知视角", "有限视角"], "fields": []}, {"key": "writingStyleDesign", "name": "文体风格设计", "desc": "确定小说的文体风格，如简洁、华丽等。", "value": ["简洁", "华丽", "幽默", "悬疑"], "fields": []}, {"key": "dialogueSystem", "name": "对话系统", "desc": "通过对话展现人物性格和推动情节发展。", "value": ["潜台词", "语音特征"], "fields": []}]}, {"key": "supportSystem", "name": "支撑系统", "desc": "为创作提供支持的因素。", "value": null, "fields": [{"key": "research", "name": "资料研究", "desc": "对历史、专业领域的资料进行研究。", "value": null, "fields": []}, {"key": "emotionMap", "name": "情绪图谱", "desc": "设计读者情感曲线。", "value": null, "fields": []}, {"key": "revisionStrategy", "name": "修订策略", "desc": "对故事进行修改，包括宏观修改和微观修改。", "value": ["宏观修改", "微观修改"], "fields": []}]}]}