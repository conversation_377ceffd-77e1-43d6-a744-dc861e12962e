{"key": "", "name": "", "desc": "", "value": null, "fields": [{"key": "character_design", "name": "人物角色设计", "desc": "角色是小说的心脏，优秀的塑造能让读者产生共鸣。本指南提供角色设计的关键要素，包括核心设定、外形、性格、背景、动机、成长轨迹、与其他角色的关系等，帮助你打造引人入胜的人物。", "value": null, "fields": [{"key": "core_concept", "name": "核心概念", "desc": "角色的核心思想，例如：复仇者、救赎者、梦想家等。它驱动着角色行动，并决定了故事的核心主题。", "value": ["复仇者", "救赎者", "梦想家", "背叛者", "守护者", "探索者", "牺牲者", "领导者", "反英雄", "导师"], "fields": []}, {"key": "appearance", "name": "外形", "desc": "角色的外貌特征，包括身高、体型、发色、眼睛颜色、穿着等。外形是角色给读者的第一印象，也能反映角色的性格、职业或社会地位。", "value": null, "fields": [{"key": "physical_description", "name": "身体描述", "desc": "详细描述角色的身体外观，例如身高、体重、体型、肤色、发型、面部特征等。", "value": null, "fields": []}, {"key": "clothing_style", "name": "服装风格", "desc": "描述角色常穿的服装风格，以及服装如何反映角色的性格、职业或社会地位。", "value": ["华丽的礼服", "朴素的粗布衣", "坚固的盔甲", "时尚的现代服装", "破旧的流浪汉服饰", "统一的制服"], "fields": []}, {"key": "distinguishing_features", "name": "显著特征", "desc": "描述角色独特的身体特征，例如疤痕、纹身、饰品等，这些特征能够增加角色的辨识度。", "value": ["独特的疤痕", "显眼的纹身", "特殊的饰品", "异色瞳孔", "残疾", "与众不同的步态"], "fields": []}]}, {"key": "personality", "name": "性格", "desc": "角色的性格特征，包括内向/外向、乐观/悲观、勇敢/懦弱等。性格决定了角色在不同情境下的行为方式。", "value": null, "fields": [{"key": "traits", "name": "性格特质", "desc": "列出角色主要的性格特质，例如：勇敢、诚实、善良、狡猾、冷酷、幽默。", "value": ["勇敢", "诚实", "善良", "狡猾", "冷酷", "幽默", "内向", "外向", "乐观", "悲观", "冲动", "谨慎"], "fields": []}, {"key": "flaws", "name": "性格缺陷", "desc": "角色的缺点，使角色更真实，也为故事制造冲突。例如：自私、嫉妒、优柔寡断。", "value": ["自私", "嫉妒", "优柔寡断", "易怒", "傲慢", "恐惧", "贪婪", "懒惰", "偏执"], "fields": []}, {"key": "strengths", "name": "性格优点", "desc": "角色的优点，帮助角色克服困难，实现目标。例如：坚韧、智慧、同情心。", "value": ["坚韧", "智慧", "同情心", "勇气", "诚实", "耐心", "乐观", "忠诚"], "fields": []}]}, {"key": "background", "name": "背景", "desc": "角色的过去经历，包括家庭、教育、社会环境等。背景塑造了角色的性格和价值观。", "value": null, "fields": [{"key": "family", "name": "家庭", "desc": "角色的家庭成员、家庭关系，以及家庭对角色的影响。", "value": ["破碎的家庭", "温馨的家庭", "贵族家庭", "贫困的家庭", "单亲家庭", "大家族"], "fields": []}, {"key": "education", "name": "教育", "desc": "角色接受的教育程度和类型，以及教育对角色的影响。", "value": ["正规的学校教育", "私塾教育", "自学", "师徒制", "无教育"], "fields": []}, {"key": "past_experiences", "name": "过往经历", "desc": "角色经历过的重大事件，例如：战争、失恋、背叛等，这些经历塑造了角色的人生观。", "value": ["战争经历", "爱情的背叛", "亲人的离世", "遭遇贫困", "成功的事业", "失败的经历"], "fields": []}]}, {"key": "motivation", "name": "动机", "desc": "角色行动的驱动力，例如：复仇、爱情、权力等。动机是角色行为的基础。", "value": ["复仇", "爱情", "权力", "金钱", "正义", "生存", "自我实现", "保护他人", "探索未知"], "fields": []}, {"key": "goals", "name": "目标", "desc": "角色想要达成的目标，可以是短期目标，也可以是长期目标。目标为故事提供方向。", "value": ["复仇成功", "获得爱情", "登上权力巅峰", "积累财富", "伸张正义", "生存下去", "实现梦想", "保护所爱之人", "探索世界"], "fields": []}, {"key": "relationships", "name": "人际关系", "desc": "角色与其他角色之间的关系，包括亲情、友情、爱情、敌对关系等。关系塑造了角色的社会环境。", "value": null, "fields": [{"key": "family_relationships", "name": "家庭关系", "desc": "角色与家庭成员之间的关系，如：父母、兄弟姐妹、子女等。", "value": ["亲密的亲子关系", "疏远的亲子关系", "兄弟姐妹间的竞争", "兄弟姐妹间的支持"], "fields": []}, {"key": "friendships", "name": "友谊", "desc": "角色与朋友之间的关系，包括友谊的建立、发展和变化。", "value": ["忠诚的友谊", "虚伪的友谊", "互相帮助的朋友", "渐行渐远的朋友"], "fields": []}, {"key": "romantic_relationships", "name": "爱情关系", "desc": "角色与爱人之间的关系，包括爱情的开始、发展、冲突和结局。", "value": ["热烈的爱情", "柏拉图式的爱情", "单相思", "背叛的爱情", "幸福的婚姻", "悲剧的爱情"], "fields": []}, {"key": "antagonistic_relationships", "name": "敌对关系", "desc": "角色与敌人之间的关系，包括冲突、对抗和最终结局。", "value": ["宿敌", "竞争对手", "误解导致的敌对", "利益冲突导致的敌对"], "fields": []}]}, {"key": "arc", "name": "成长弧光", "desc": "角色在故事中的变化和成长过程。角色弧光是故事的核心，角色通过经历事件而改变。", "value": null, "fields": [{"key": "beginning", "name": "开始", "desc": "角色在故事开始时的状态，包括性格、价值观和目标。", "value": ["坚定的信念", "迷茫的开始", "充满希望的开始", "悲观的开端"], "fields": []}, {"key": "middle", "name": "中间", "desc": "角色在故事发展过程中遇到的挑战、冲突和转变。", "value": ["经历挫折", "做出选择", "改变价值观", "学会成长"], "fields": []}, {"key": "end", "name": "结尾", "desc": "角色在故事结束时的状态，以及角色在故事中获得的成长和改变。", "value": ["达成目标", "改变价值观", "获得救赎", "走向毁灭"], "fields": []}]}]}]}