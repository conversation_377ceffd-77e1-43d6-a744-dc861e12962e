{"key": "", "name": "", "desc": "", "value": null, "fields": [{"key": "novel_outline", "name": "小说大纲设计", "desc": "小说大纲是创作的蓝图，它规划了故事的结构、人物、情节和主题。一个好的大纲能帮助作者保持创作方向，避免故事失控，并提升整体质量。", "value": null, "fields": [{"key": "story_concept", "name": "故事概念", "desc": "故事的核心想法，包括类型、主题、冲突和目标。这是故事的灵魂。", "value": null, "fields": [{"key": "genre", "name": "小说类型", "desc": "小说所属的类型，如科幻、奇幻、悬疑、爱情等。", "value": ["科幻", "奇幻", "悬疑", "爱情", "历史", "武侠", "都市", "恐怖", "惊悚", "冒险"], "fields": []}, {"key": "theme", "name": "主题", "desc": "故事想要表达的核心思想或信息，例如：爱、复仇、成长、救赎。", "value": ["爱与牺牲", "复仇与救赎", "成长与蜕变", "正义与邪恶的斗争", "人性的探索", "科技与伦理的冲突"], "fields": []}, {"key": "conflict", "name": "冲突", "desc": "故事中推动情节发展的主要矛盾，如个人与社会、人与自然、人物之间的冲突。", "value": ["个人与社会制度的冲突", "人物之间的情感冲突", "主人公内心的挣扎", "人与自然的对抗", "不同价值观的碰撞"], "fields": []}, {"key": "goal", "name": "目标", "desc": "主人公在故事中想要达成的目标，以及实现目标的阻碍。", "value": ["拯救世界", "复仇成功", "获得真爱", "揭露真相", "实现自我价值", "改变命运"], "fields": []}]}, {"key": "character", "name": "人物", "desc": "塑造鲜活的人物是故事成功的关键。包括主人公、配角、反派等，以及他们的性格、目标和关系。", "value": null, "fields": [{"key": "protagonist", "name": "主人公", "desc": "故事的核心人物，推动故事发展，面临挑战并最终成长。", "value": null, "fields": [{"key": "personality", "name": "性格", "desc": "主人公的性格特点，例如：勇敢、善良、聪明、狡猾等。", "value": ["勇敢", "善良", "聪明", "狡猾", "内向", "外向", "坚韧", "脆弱"], "fields": []}, {"key": "goal", "name": "目标", "desc": "主人公在故事中想要达成的目标。", "value": ["拯救世界", "复仇", "获得爱情", "找到真相", "改变命运"], "fields": []}, {"key": "flaw", "name": "弱点", "desc": "主人公的弱点，使人物更真实，并制造故事的冲突。", "value": ["冲动", "犹豫", "自负", "恐惧", "贪婪", "优柔寡断"], "fields": []}, {"key": "arc", "name": "人物弧光", "desc": "主人公在故事中的转变和成长。", "value": ["从弱小到强大", "从迷茫到坚定", "从自私到无私", "从仇恨到宽恕"], "fields": []}]}, {"key": "supporting_characters", "name": "配角", "desc": "辅助主人公的角色，推动情节发展，丰富故事内容。", "value": null, "fields": [{"key": "role", "name": "角色定位", "desc": "配角在故事中的作用，例如：导师、朋友、对手等。", "value": ["导师", "朋友", "对手", "爱人", "家人", "敌人"], "fields": []}]}, {"key": "antagonist", "name": "反派", "desc": "与主人公对抗的角色，制造冲突，推动故事发展。", "value": null, "fields": [{"key": "motivation", "name": "动机", "desc": "反派行动的理由和目的。", "value": ["权力欲", "复仇", "野心", "保护自己", "拯救世界（以错误的方式）"], "fields": []}]}, {"key": "character_relationships", "name": "人物关系", "desc": "人物之间的关系，如友谊、爱情、敌对等，影响故事发展。", "value": null, "fields": [{"key": "relationship_types", "name": "关系类型", "desc": "人物之间的关系，例如：朋友、敌人、恋人、家人等。", "value": ["友谊", "爱情", "亲情", "敌对", "师徒", "合作"], "fields": []}]}]}, {"key": "plot", "name": "情节", "desc": "故事的事件发展，包括开端、发展、高潮、结局。情节是故事的骨架。", "value": null, "fields": [{"key": "structure", "name": "结构", "desc": "故事的整体结构，例如：三幕剧、起承转合等。", "value": ["三幕剧", "起承转合", "倒叙", "多线叙事"], "fields": []}, {"key": "beginning", "name": "开端", "desc": "介绍背景、人物、建立故事的基调，并埋下伏笔。", "value": null, "fields": []}, {"key": "rising_action", "name": "发展", "desc": "情节逐渐推进，冲突升级，人物面临挑战。", "value": null, "fields": []}, {"key": "climax", "name": "高潮", "desc": "故事中最紧张、最关键的时刻，冲突达到顶点。", "value": null, "fields": []}, {"key": "falling_action", "name": "结局", "desc": "高潮后的情节，解决冲突，揭示主题，并为故事画上句号。", "value": null, "fields": []}, {"key": "plot_points", "name": "情节点", "desc": "故事中重要的转折点，推动情节发展。", "value": null, "fields": [{"key": "plot_point_examples", "name": "情节点示例", "desc": "一些常见的情节点，例如：事件发生、转折点、揭露真相等。", "value": ["事件发生", "转折点", "揭露真相", "人物做出重要决定", "关键物品的出现"], "fields": []}]}]}, {"key": "setting", "name": "场景", "desc": "故事发生的时间和地点，影响故事氛围和人物行动。", "value": null, "fields": [{"key": "time_period", "name": "时间", "desc": "故事发生的时间，例如：古代、现代、未来等。", "value": ["古代", "现代", "未来", "架空世界", "历史时期"], "fields": []}, {"key": "location", "name": "地点", "desc": "故事发生的地点，例如：城市、乡村、宇宙等。", "value": ["城市", "乡村", "宇宙", "奇幻世界", "历史场景"], "fields": []}, {"key": "atmosphere", "name": "氛围", "desc": "场景营造的氛围，例如：紧张、浪漫、神秘等。", "value": ["紧张", "浪漫", "神秘", "恐怖", "轻松", "悲伤"], "fields": []}]}, {"key": "world_building", "name": "世界观构建", "desc": "对于奇幻、科幻等类型的小说，需要构建独特的背景设定，包括规则、文化等。", "value": null, "fields": [{"key": "rules", "name": "规则", "desc": "世界运行的规则，例如：魔法系统、科技发展水平等。", "value": ["魔法系统", "科技发展水平", "社会制度", "经济体系"], "fields": []}, {"key": "culture", "name": "文化", "desc": "世界的文化、习俗、宗教等。", "value": ["习俗", "宗教", "语言", "艺术"], "fields": []}]}, {"key": "writing_style", "name": "写作风格", "desc": "作者的写作风格，例如：第一人称、第三人称，以及语言风格。", "value": null, "fields": [{"key": "perspective", "name": "视角", "desc": "故事的叙述视角，例如：第一人称、第三人称等。", "value": ["第一人称", "第三人称（限定）", "第三人称（全知）", "第二人称"], "fields": []}, {"key": "tone", "name": "基调", "desc": "故事的整体基调，例如：幽默、严肃、悲伤等。", "value": ["幽默", "严肃", "悲伤", "轻松", "悬疑"], "fields": []}, {"key": "language", "name": "语言", "desc": "使用的语言风格，例如：简洁、华丽等。", "value": ["简洁", "华丽", "口语化", "正式"], "fields": []}]}]}]}