{"key": "novelGuide", "name": "小说创作指南", "desc": "包含小说创作所需的所有核心元素的结构化指南", "value": "json", "isArray": false, "fields": [{"key": "coreIdea", "name": "核心创意", "desc": "故事的核心概念和独特卖点", "value": "string", "isArray": false, "fields": []}, {"key": "structure", "name": "故事结构", "desc": "小说的整体叙事框架", "value": ["三幕式", "英雄之旅"], "isArray": false, "fields": []}, {"key": "outline", "name": "详细大纲", "desc": "章节级别的故事发展概要", "value": "array", "isArray": true, "fields": [{"key": "chapter", "name": "章节", "desc": "单个章节的概要", "value": "string", "isArray": false, "fields": []}]}, {"key": "characters", "name": "角色设定", "desc": "故事中的所有主要角色", "value": "array", "isArray": true, "fields": [{"key": "name", "name": "角色名", "desc": "角色的全名", "value": "string", "isArray": false, "fields": []}, {"key": "role", "name": "角色类型", "desc": "角色在故事中的功能", "value": ["主角", "反派", "配角", "导师", "盟友", "恋人", "小丑"], "isArray": false, "fields": []}, {"key": "appearance", "name": "外貌特征", "desc": "角色的物理特征", "value": "string", "isArray": false, "fields": []}, {"key": "personality", "name": "性格特点", "desc": "角色的性格特征", "value": "string", "isArray": false, "fields": []}, {"key": "background", "name": "背景故事", "desc": "角色的历史和动机", "value": "string", "isArray": false, "fields": []}]}, {"key": "props", "name": "关键道具", "desc": "故事中的重要物品", "value": "array", "isArray": true, "fields": [{"key": "name", "name": "道具名称", "desc": "物品的正式名称", "value": "string", "isArray": false, "fields": []}, {"key": "type", "name": "道具类型", "desc": "物品的分类", "value": ["武器", "神器", "日常用品", "服饰", "交通工具", "文件", "艺术品"], "isArray": false, "fields": []}, {"key": "significance", "name": "重要性", "desc": "道具对剧情的影响", "value": "string", "isArray": false, "fields": []}]}, {"key": "scenes", "name": "关键场景", "desc": "故事中的重要地点", "value": "array", "isArray": true, "fields": [{"key": "name", "name": "场景名称", "desc": "地点的名称", "value": "string", "isArray": false, "fields": []}, {"key": "type", "name": "场景类型", "desc": "地点的分类", "value": ["城市", "乡村", "荒野", "建筑内部", "交通工具", "梦境", "幻想世界"], "isArray": false, "fields": []}, {"key": "atmosphere", "name": "氛围", "desc": "场景的情绪基调", "value": ["欢快", "阴森", "紧张", "浪漫", "神秘", "压抑", "庄严"], "isArray": false, "fields": []}]}, {"key": "materialLibrary", "name": "素材库", "desc": "创作参考资料的集合", "value": "json", "isArray": false, "fields": [{"key": "research", "name": "研究资料", "desc": "背景调查材料", "value": "string", "isArray": true, "fields": []}, {"key": "inspirations", "name": "灵感来源", "desc": "创意启发点", "value": "string", "isArray": true, "fields": []}, {"key": "visualReferences", "name": "视觉参考", "desc": "图像和视觉素材", "value": "string", "isArray": true, "fields": []}]}, {"key": "style", "name": "文体风格", "desc": "叙事的语言和风格特征", "value": "json", "isArray": false, "fields": [{"key": "narrativePerspective", "name": "叙事视角", "desc": "故事的讲述角度", "value": ["第一人称", "第二人称", "第三人称有限", "第三人称全知", "多重视角"], "isArray": false, "fields": []}, {"key": "tone", "name": "语气", "desc": "叙事的整体态度", "value": ["严肃", "幽默", "讽刺", "忧郁", "激昂", "超然"], "isArray": false, "fields": []}, {"key": "pace", "name": "节奏", "desc": "故事发展的速度", "value": ["快速", "适中", "缓慢", "多变"], "isArray": false, "fields": []}, {"key": "languageStyle", "name": "语言风格", "desc": "用词和句法特征", "value": ["简洁", "华丽", "口语化", "诗意", "技术性", "古风"], "isArray": false, "fields": []}]}]}