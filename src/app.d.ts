import type { DecodedIdToken } from "firebase-admin/auth";

// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}

		/**
		 * Defines the custom data that can be accessed in server-side hooks and load functions.
		 * @see https://kit.svelte.dev/docs/types#app-locals
		 */
		interface Locals {
			/**
			 * Holds the authenticated user's data, decoded from the session cookie.
			 * It is `null` if the user is not authenticated.
			 */
			user: DecodedIdToken | null;
		}

		// interface PageData {}
		// interface Platform {}
	}
}

export {};