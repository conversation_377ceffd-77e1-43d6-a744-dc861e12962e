import { env } from "$env/dynamic/private";
import type { GeminiRequest } from "$lib/models/gemini.model";
import type { ChatCompletion } from "openai/resources";
import type { ChatCompletionCreateParamsNonStreaming } from "openai/resources.js";

export interface ChatCompletionResponse {
    success: boolean;
    status: number;
    data?: ChatCompletion;
    error?: any;
}

export async function chatCompletions(signal: AbortSignal, requestBody: ChatCompletionCreateParamsNonStreaming): Promise<ChatCompletionResponse> {
    const model = requestBody.model;
    let url = "";
    let token = "";
    if (model.includes("gemini")) {
        url = "https://generativelanguage.googleapis.com/v1beta/chat/completions";
        token = env.GEMINI_API_KEY;
    } else if (model.includes("deepseek")) {
        url = "https://api.deepseek.com/chat/completions";
        token = env.DEEPSEEK_API_KEY;
    }

    try {
        const resp = await fetch(url,
            {
                signal: signal,
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: "Bearer " + token,
                },
                body: JSON.stringify(requestBody),
            }
        );

        const data = await resp.json();
        if (resp.ok) {
            return {
                success: resp.ok,
                status: resp.status,
                data: data as ChatCompletion,
            }
        }
        return {
            success: resp.ok,
            status: resp.status,
            error: data,
        }
    } catch (error) {
        console.error("OpenAI error:", error);
        throw error;
    }
}

// gemini 
// doc: https://ai.google.dev/gemini-api/docs/openai?hl=zh-cn
// thinking: https://ai.google.dev/gemini-api/docs/thinking?hl=zh-cn
// text: https://ai.google.dev/gemini-api/docs/text-generation?hl=zh-cn
export async function gemini(signal: AbortSignal, model: string, request: GeminiRequest) {
    const resp = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`,
        {
            signal: signal,
            method: "POST",
            headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
                "x-goog-api-key": env.GEMINI_API_KEY,
            },
            body: JSON.stringify(request),
        }
    );
    const headers = new Headers(resp.headers);
    headers.delete('content-encoding');
    headers.delete('content-length');

    return new Response(resp.body, {
        status: resp.status,
        headers: headers,
    });
}