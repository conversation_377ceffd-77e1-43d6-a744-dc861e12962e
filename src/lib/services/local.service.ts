import { browser } from "$app/environment";
import { writable } from "svelte/store";

// 创建可写store并添加localStorage持久化
export const createPersistedStore = <T>(key: string, defaultValue: T) => {
    const store = writable<T>(defaultValue);

    if (browser) {
        const saved = localStorage.getItem(key);
        if (saved != null) {
            let value = saved;
            try {
                value = JSON.parse(saved);
            } catch { } finally {
                store.set(value as T);
            }
        }

        store.subscribe(value => {
            localStorage.setItem(key, JSON.stringify(value));
        });
    }

    return store;
};
