// 提示词模板系统

export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  variables: string[];
  category: 'novel' | 'character' | 'plot' | 'dialogue' | 'description' | 'general';
  tags: string[];
  examples?: Array<{
    input: Record<string, string>;
    output: string;
  }>;
}

// 内置提示词模板
export const builtinPrompts: PromptTemplate[] = [
  {
    id: 'novel_summary',
    name: '小说简介生成',
    description: '根据基本信息生成吸引人的小说简介',
    template: `请根据以下信息生成一个吸引人的小说简介：

标题：{{title}}
类型：{{genre}}
主角：{{protagonist}}
背景设定：{{setting}}
核心冲突：{{conflict}}

要求：
1. 简介应该在100-200字之间
2. 突出故事的独特性和吸引力
3. 制造悬念，让读者想要继续阅读
4. 语言生动，富有感染力

请以JSON格式返回：
\`\`\`json
{
  "summary": "生成的小说简介"
}
\`\`\``,
    variables: ['title', 'genre', 'protagonist', 'setting', 'conflict'],
    category: 'novel',
    tags: ['简介', '营销', '吸引力'],
    examples: [
      {
        input: {
          title: '星际迷航',
          genre: '科幻',
          protagonist: '年轻的宇航员',
          setting: '未来太空',
          conflict: '外星入侵'
        },
        output: '在2157年的宇宙深处，年轻的宇航员林克意外发现了一个古老的外星文明遗迹...'
      }
    ]
  },
  {
    id: 'character_profile',
    name: '角色档案生成',
    description: '创建详细的角色背景和性格档案',
    template: `请为以下角色创建详细的档案：

角色名称：{{name}}
年龄：{{age}}
职业：{{occupation}}
性格特点：{{personality}}
背景故事：{{background}}

请生成包含以下内容的角色档案：
1. 基本信息（姓名、年龄、外貌特征）
2. 性格分析（优点、缺点、行为模式）
3. 背景故事（成长经历、重要事件）
4. 人际关系（家庭、朋友、敌人）
5. 目标动机（短期目标、长期愿望）
6. 口头禅或特殊习惯

请以JSON格式返回：
\`\`\`json
{
  "basicInfo": {
    "name": "角色姓名",
    "age": "年龄",
    "appearance": "外貌描述"
  },
  "personality": {
    "traits": ["性格特点"],
    "strengths": ["优点"],
    "weaknesses": ["缺点"]
  },
  "background": "背景故事",
  "relationships": {
    "family": "家庭关系",
    "friends": "朋友关系",
    "enemies": "敌对关系"
  },
  "motivation": {
    "shortTerm": "短期目标",
    "longTerm": "长期愿望"
  },
  "habits": ["特殊习惯或口头禅"]
}
\`\`\``,
    variables: ['name', 'age', 'occupation', 'personality', 'background'],
    category: 'character',
    tags: ['角色', '人物', '背景'],
  },
  {
    id: 'plot_outline',
    name: '情节大纲生成',
    description: '根据故事要素生成详细的情节大纲',
    template: `请根据以下信息生成详细的故事大纲：

故事类型：{{genre}}
主要角色：{{characters}}
核心冲突：{{conflict}}
故事背景：{{setting}}
预期长度：{{length}}

请按照经典的三幕式结构生成大纲：

第一幕（开端）：
- 建立世界观和角色
- 引入核心冲突
- 设置故事钩子

第二幕（发展）：
- 冲突升级
- 角色成长
- 转折点

第三幕（高潮与结局）：
- 最终对决
- 冲突解决
- 角色弧光完成

请以JSON格式返回：
\`\`\`json
{
  "title": "建议的故事标题",
  "acts": [
    {
      "act": 1,
      "name": "开端",
      "chapters": [
        {
          "chapter": 1,
          "title": "章节标题",
          "summary": "章节概要",
          "keyEvents": ["关键事件"]
        }
      ]
    }
  ],
  "themes": ["主要主题"],
  "characterArcs": {
    "角色名": "角色发展弧线"
  }
}
\`\`\``,
    variables: ['genre', 'characters', 'conflict', 'setting', 'length'],
    category: 'plot',
    tags: ['大纲', '结构', '情节'],
  },
  {
    id: 'dialogue_enhancement',
    name: '对话优化',
    description: '改进对话的自然度和角色特色',
    template: `请优化以下对话，使其更加自然、生动，并体现角色特色：

原始对话：
{{original_dialogue}}

角色信息：
{{character_info}}

优化要求：
1. 保持对话的核心意思不变
2. 增加角色的个性特色
3. 使语言更加自然流畅
4. 适当添加动作描写和心理活动
5. 注意对话的节奏感

请以JSON格式返回：
\`\`\`json
{
  "optimized_dialogue": "优化后的对话",
  "improvements": ["改进说明"],
  "character_voice": "角色语言特色分析"
}
\`\`\``,
    variables: ['original_dialogue', 'character_info'],
    category: 'dialogue',
    tags: ['对话', '优化', '角色'],
  }
];

// 提示词变量替换
export function fillPromptTemplate(template: string, variables: Record<string, string>): string {
  let result = template;
  
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{${key}}}`, 'g');
    result = result.replace(regex, value || `[${key}]`);
  }
  
  return result;
}

// 验证提示词模板
export function validatePromptTemplate(template: PromptTemplate): string[] {
  const errors: string[] = [];
  
  if (!template.id) errors.push('缺少模板ID');
  if (!template.name) errors.push('缺少模板名称');
  if (!template.template) errors.push('缺少模板内容');
  
  // 检查模板中的变量是否在variables数组中定义
  const templateVars = template.template.match(/{{(\w+)}}/g) || [];
  const definedVars = template.variables || [];
  
  for (const varMatch of templateVars) {
    const varName = varMatch.slice(2, -2);
    if (!definedVars.includes(varName)) {
      errors.push(`未定义的变量: ${varName}`);
    }
  }
  
  return errors;
}

// 获取提示词模板
export function getPromptTemplate(id: string): PromptTemplate | null {
  return builtinPrompts.find(p => p.id === id) || null;
}

// 按类别获取提示词模板
export function getPromptsByCategory(category: PromptTemplate['category']): PromptTemplate[] {
  return builtinPrompts.filter(p => p.category === category);
}

// 搜索提示词模板
export function searchPrompts(query: string): PromptTemplate[] {
  const lowerQuery = query.toLowerCase();
  return builtinPrompts.filter(p => 
    p.name.toLowerCase().includes(lowerQuery) ||
    p.description.toLowerCase().includes(lowerQuery) ||
    p.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
  );
}
