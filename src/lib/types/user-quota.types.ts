/**
 * 用户配额管理系统类型定义
 * 
 * 用于管理用户的AI配额使用情况和Token消费记录
 */

import type { QuotaType, ActivationCodeType } from './activation-code.types';
import type { CompletionUsage } from 'openai/resources';

// 用户基本信息
export interface UserInfo {
  uid: string;
  email: string;
  displayName?: string;
  emailVerified: boolean;
  photoURL?: string;
  createdAt?: string;
  lastLoginAt?: string;
  disabled?: boolean;
}

// 用户配额池中的单个配额项
export interface UserQuotaItem {
  id: string;                    // 配额来源ID（激活码ID）
  type: ActivationCodeType;      // 配额类型：bonus 或 plan
  amount: {
    agent: number;               // AI代理总额度
    completion: number;          // 文本补全总额度
  };
  used: {
    agent: number;               // AI代理已使用额度
    completion: number;          // 文本补全已使用额度
  };
  created_at: string;            // 配额创建时间
  expires_at?: string;           // 配额过期时间（仅plan类型）
  activated_at?: string;         // 激活时间
}

// 用户完整配额信息
export interface UserQuotaInfo extends UserInfo {
  quotaPool: UserQuotaItem[];    // 配额池
  totalQuota: {
    agent: {
      total: number;             // 总额度
      used: number;              // 已使用
      remaining: number;         // 剩余
    };
    completion: {
      total: number;
      used: number;
      remaining: number;
    };
  };
  totalTokens: number;
  lastUsedAt?: string;           // 最后使用时间
}

export interface TokenUsageState extends CompletionUsage {
  model: string;
  created: number;
}

// Token使用记录
export interface TokenUsageRecord {
  id: string;                    // 记录ID
  uid: string;                   // 用户ID
  email: string;                 // 用户邮箱
  used: number;                  // 使用次数（通常为1）
  model: QuotaType;              // 使用的模型类型
  quotaId: string;               // 消费的配额ID
  tokenUsage: TokenUsageState[]; // OpenAI Token使用详情
  totalTokens: number;           // 总Token数
  promptTokens: number;          // 提示Token数
  completionTokens: number;      // 完成Token数
  createdAt: number;             // 创建时间戳
}

export interface DateUsageStats {
  // 日期 YYYY-MM-DD
  // 周 YYYY-WW
  // 月 YYYY-MM
  // 年 YYYY
  date: string;
  usage: {
    model: QuotaType;
    count: number;
    totalTokens: number;           // 总Token数
    promptTokens: number;          // 提示Token数
    completionTokens: number;      // 完成Token数
    promptAverage: number;         // 提示Token平均值
  }[];
  tokens: number;
}

export interface QuotaTokenUsage {
  reqCount: number;            // ai 调用次数
  totalTokens: number;         // 总Token数
  promptTokens: number;        // 提示Token数
  completionTokens: number;    // 完成Token数
}

// 用户使用统计
export interface UserUsageStats {
  uid: string;
  email: string;
  totalUsage: {
    agent: number;               // AI代理总使用次数
    completion: number;          // 文本补全总使用次数
  };
  tokenStats: {
    totalTokens: number;         // 总Token数
    agent: QuotaTokenUsage,
    completion: QuotaTokenUsage,
  };
  recendUsage: Array<DateUsageStats>;
  dailyUsage: Array<DateUsageStats>;
  lastUsedAt?: string;
}

// 系统整体统计
export interface SystemStats {
  totalUsers: number;            // 总用户数
  activeUsers: number;           // 活跃用户数（最近30天有使用）
  totalQuotaDistributed: {
    agent: number;
    completion: number;
  };
  totalQuotaUsed: {
    agent: number;
    completion: number;
  };
  totalTokensConsumed: number;
  averageTokensPerUser: number;
  topUsers: Array<{
    uid: string;
    email: string;
    totalUsage: number;
    totalTokens: number;
  }>;
}

// 配额操作类型
export enum QuotaOperationType {
  ADD = 'add',                   // 增加配额
  SUBTRACT = 'subtract',         // 减少配额
  RESET = 'reset',               // 重置配额
  EXPIRE = 'expire'              // 过期配额
}

// 配额操作请求
export interface QuotaOperationRequest {
  operation: QuotaOperationType;
  quotaType: QuotaType;
  amount?: number;               // 操作数量（add/subtract时必需）
  reason?: string;               // 操作原因
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 具体的API响应类型
export type SystemStatsResponse = ApiResponse<SystemStats>;
