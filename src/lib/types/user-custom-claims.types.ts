import type { ActivationCodeType } from "./activation-code.types";

interface _CompletionUsage {
    req_count: number; // AI 请求调用次数, 主要用于 ai agent 模式
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
}

export interface UserCompletionUsage {
    total_tokens: number;
    agent: _CompletionUsage,
    completion: _CompletionUsage,
    last_used_at?: string;
}

export interface UserQuote {
    id: string;
    type: ActivationCodeType;
    amount: {
        agent: number;
        completion: number;
    };
    used: {
        agent: number;
        completion: number;
    };
    created_at: string;
    expires_at?: string; // 如果为空, 则表示不限制, 没有过期时间
}

export interface UserCustomClaims {
    quota_pool?: UserQuote[];
    token_usage?: UserCompletionUsage;
}
