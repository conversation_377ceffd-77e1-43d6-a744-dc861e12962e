export interface GeminiConfig {
  temperature?: number | undefined;
  maxOutputTokens?: number;
  topP?: number | undefined;
  thinkingConfig: {
    // thinking 模式下的配置, 
    // 0: 表示关闭, 
    // -1: 表示动态思维, 不限模型输出长度, 
    // 正值(>0): 表示开启 thinking 模式并限制输出长度
    thinkingBudget?: number;
    // 是否开启思维摘要, 
    // 启动后, 响应体里里的 thought 值为 true, 表示该 part 为思维摘要
    includeThoughts?: boolean;
  };
}

export interface GeminiRequest {
  contents: {
    role: string;
    parts: { text: string }[];
  }[];
  generationConfig: GeminiConfig;
  safetySettings: {
    category: string;
    threshold: string;
  }[];
}

export interface GeminiResponse {
  candidates?: {
    content: {
      parts: {
        thought: boolean;
        text: string;
      }[];
    };
  }[];
}

// createGeminiRequest 返回 GeminiRequest
export function createGeminiRequest(texts: string[], generationConfig?: GeminiConfig): GeminiRequest {
  const parts = texts.map((item) => ({ text: item }));
  return {
    contents: [
      {
        role: "user",
        parts,
      },
    ],
    generationConfig: {
      temperature: generationConfig?.temperature,
      maxOutputTokens: generationConfig?.maxOutputTokens ?? 6000,
      topP: generationConfig?.topP,
      thinkingConfig: generationConfig?.thinkingConfig || {},
    },
    safetySettings: [
      { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
    ],
  };
}

// 拼接 GeminiResponse 对象为字符串
export function joinGeminiResponse(res: GeminiResponse): string {
  return res.candidates?.[0]?.content?.parts?.map((item) => item.text).join("") ?? "";
}