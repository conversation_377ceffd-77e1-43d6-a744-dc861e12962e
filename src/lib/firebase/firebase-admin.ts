import admin from 'firebase-admin';
import { env } from "$env/dynamic/private";
import { ActivationCodeType, QuotaType, type ActivationCode } from '$lib/types/activation-code.types';
import type { ChatCompletionResponse } from '$lib/services/server.service';
import type { UserCompletionUsage, UserQuote, UserCustomClaims } from '$lib/types/user-custom-claims.types';
import { calcSubscriptionPeriod } from '$lib/utils/activation-code.utils';
import type { TokenUsageRecord, TokenUsageState } from '$lib/types/user-quota.types';

// 配置邮件链接设置
// 身份认证 rest api docs
// https://cloud.google.com/identity-platform/docs/use-rest-api?hl=zh-cn
// 邮箱链接认证请求回调地址
// 该页面会请求 https://www.googleapis.com/identitytoolkit/v3/relyingparty/getProjectConfig?key=AIzaSyB3EAt_QmIvQLmnsgRcEklmKPXwbU05ymw
// https://ai-novel-983d1.firebaseapp.com/__/auth/action?apiKey=AIzaSyB3EAt_QmIvQLmnsgRcEklmKPXwbU05ymw&mode=signIn&oobCode=n2bfvW4wRiyUFHe8MmqturQkvj-rRodujU-wI4A0qwsAAAGYdf5sAw&continueUrl=http://localhost:3004/login/callback&lang=en
export function initializeFirebaseAdmin() {
    const serviceAccount = JSON.parse(
        env.GOOGLE_FIREBASE_ADMIN_SERVICE_ACCOUNT
    );

    if (!admin.apps || !admin.apps.length) {
        admin.initializeApp({
            credential: admin.credential.cert(serviceAccount), // Your service account JSON
        });
    }
    return { fireauth: admin.auth(), firedb: admin.firestore() }
}

/**
 * 使用 AI 配额, 处理认证及记录配额使用情况
 * @param token 用户认证 token
 * @param type 配额使用类型
 * @param action 动作
 * @returns 返回的结果
 */
export async function useQuota(token: string, type: QuotaType, action: () => Promise<ChatCompletionResponse>) {
    const { fireauth, firedb } = initializeFirebaseAdmin();
    const user = await fireauth.verifyIdToken(token);
    const record = await fireauth.getUser(user.uid);
    const customClaims = (record.customClaims || {}) as UserCustomClaims;
    const quotaPool: UserQuote[] = customClaims.quota_pool || [];
    const tokenUsage: UserCompletionUsage = customClaims.token_usage ?? {
        agent: {
            req_count: 0,
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0,
        },
        completion: {
            req_count: 0,
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0,
        },
        total_tokens: 0,
        last_used_at: new Date().toUTCString(),
    };
    // console.log("get user", quotaPool);

    if (!quotaPool || 0 == quotaPool.length) {
        return new Response(JSON.stringify({ success: false, error: "No quota available (100401)" }), { status: 401 });
    }
    // 未使用的额度
    let unusage = 0;
    quotaPool.forEach(quota => {
        // 判断配额是否在有效期内
        if (!quota.expires_at || new Date() < new Date(quota.expires_at)) {
            switch (type) {
                case QuotaType.AGENT:
                    unusage += quota.amount.agent - quota.used.agent;
                    break;
                case QuotaType.COMPLETION:
                    unusage += quota.amount.completion - quota.used.completion;
                    break;
                default:
                    break;
            }
        }
    });
    if (unusage <= 0) {
        return new Response(JSON.stringify({ success: false, error: "No quota available (101401)" }), { status: 401 });
    }
    const response = await action();
    // 优先消耗订阅计划里的额度
    let index = quotaPool.findIndex(quota => quota.type === ActivationCodeType.PLAN && quota.used[type] < quota.amount[type]);
    if (-1 == index) {
        index = quotaPool.findIndex(quota => quota.type === ActivationCodeType.BONUS && quota.used[type] < quota.amount[type]);
    }
    if (0 <= index) {
        quotaPool[index].used[type] += 1;
    }
    tokenUsage[type].prompt_tokens += response.data?.usage?.prompt_tokens || 0;
    tokenUsage[type].completion_tokens += response.data?.usage?.completion_tokens || 0;
    tokenUsage[type].total_tokens += response.data?.usage?.total_tokens || 0;
    tokenUsage[type].req_count += 1;
    tokenUsage.total_tokens += response.data?.usage?.total_tokens || 0;
    tokenUsage.last_used_at = new Date().toUTCString();

    const updatedClaims = { ...customClaims, quota_pool: quotaPool, token_usage: tokenUsage };
    fireauth.setCustomUserClaims(user.uid, updatedClaims);

    const usage = response.data?.usage ? {
        ...response.data.usage,
        model: response.data.model,
        created: response.data.created,
    } : {} as TokenUsageState;

    const now = Date.now();
    const docId = `${user.uid}_${now}`
    const docRef = firedb.collection('user_token_usages').doc(docId);
    // 创建用户 token 使用数据
    const userTokenUsage = {
        uid: user.uid,
        email: user.email || record.email,
        used: 1,
        model: type,
        // 来源
        quotaId: quotaPool[index].id,
        // token 使用情况, 以每次调用 ai 为一组
        tokenUsage: usage ? [usage] : [],
        totalTokens: usage.total_tokens || 0,
        promptTokens: usage.prompt_tokens || 0,
        completionTokens: usage.completion_tokens || 0,
        createdAt: now
    } as TokenUsageRecord;
    // 保存到数据库
    await docRef.set(userTokenUsage);

    return new Response(JSON.stringify(response.data ? response.data : response.error), {
        status: response.status,
        headers: {
            'Content-Type': 'application/json'
        }
    });
}


export async function validActivationCode(code: string) {
    const db = admin.firestore();
    const collectionRef = db.collection("activation_codes");

    // 获取文档引用
    let docRef = collectionRef.doc(code);
    // 使用 get() 获取文档快照
    try {
        const docSnap = await docRef.get();

        return {
            isValid: docSnap.exists,
            snapshot: docSnap
        }
    } catch (error) {
        console.error("Admin SDK 获取文档时出错：", error);
        return {
            isValid: false,
            snapshot: null
        }
    }
}

export async function validUserToken(email: string, token?: string) {
    if (!token || token.trim().length == 0) return false;
    const decodedToken = await admin.auth().verifyIdToken(token);
    return decodedToken.email ? decodedToken.email === email : false;
}

export async function updateUserActivationCode(email: string, userToken: string, activationCode: string) {
    if (!validUserToken(email, userToken)) {
        return { success: false, err: "Invalid user token" };
    }
    try {
        const user = await admin.auth().getUserByEmail(email);

        const db = admin.firestore();
        const docRef = db.collection('activation_codes').doc(activationCode);
        const doc = await docRef.get();

        if (!doc.exists) {
            return { success: false, err: "Invalid activation code (102)" };
        }

        const pendingActivationCodeData = doc.data() as ActivationCode;
        const now = Date.now();

        const isExpired = pendingActivationCodeData.expireAt < now;
        if (isExpired) {
            return { success: false, err: "Activation code expired (105)" };
        }

        if (pendingActivationCodeData.isActive) {
            return { success: false, err: "Activation code already used (103)" };
        }

        const customClaims = (user.customClaims || {}) as UserCustomClaims;
        const quotaPool: UserQuote[] = customClaims.quota_pool || [];

        const isAdded = quotaPool.find(quota => quota.id === activationCode);
        if (isAdded) {
            return { success: false, err: "Activation code already added (104)" };
        }

        const hasPlan = quotaPool.find(quota => quota.type === ActivationCodeType.PLAN);
        if (hasPlan && pendingActivationCodeData.type === ActivationCodeType.PLAN) {
            return { success: false, err: "Plan already exists (106)" };
        }

        pendingActivationCodeData.activatedAt = now;
        pendingActivationCodeData.activatedEmail = email;
        pendingActivationCodeData.isActive = true;

        const startAt = new Date();
        const { endAt } = calcSubscriptionPeriod(startAt, pendingActivationCodeData.validity);
        const expiresAt = pendingActivationCodeData.validity === 0 ? "" : endAt.toUTCString();
        quotaPool.push({
            id: activationCode,
            type: pendingActivationCodeData.type,
            amount: {
                agent: pendingActivationCodeData.amount.agent,
                completion: pendingActivationCodeData.amount.completion
            },
            used: {
                agent: 0,
                completion: 0
            },
            created_at: startAt.toUTCString(),
            expires_at: expiresAt,
        });

        await docRef.update(pendingActivationCodeData as any);
        const updatedClaims: UserCustomClaims = { ...customClaims, quota_pool: quotaPool };
        await admin.auth().setCustomUserClaims(user.uid, updatedClaims);
        return { success: true, err: null };
    } catch (error) {
        console.error("Admin SDK 更新用户激活码时出错：", error);
        return { success: false, err: JSON.stringify(error) };
    }
}