import { userAuth } from "$lib/stores/app.store";
import { get } from "svelte/store";
import { type IAuthStrategy, type AuthResponse, type AuthUser, type AuthToast, AuthMethod } from "./auth-strategy.types";
import { AuthStrategy } from "./auth-strategy.types";
import { InputValidator } from "$lib/utils/security";
import { browser } from "$app/environment";
import { fireapp } from "$lib/firebase/firebase";
import {
  signOut as firebaseSignOut,
} from "firebase/auth";
import { env } from "$env/dynamic/public";

// Firebase REST API 端点
const API_BASE_URL = "/api/googleapis?rest=";
const BASE_URL = "https://identitytoolkit.googleapis.com";
const SIGN_UP_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signUp";
const SIGN_IN_PASSWORD_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword";
const SIGN_IN_EMAIL_LINK_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithEmailLink";
const SEND_OOB_CODE_URL = "https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode";
const UPDATE_ACCOUNT_URL = "https://identitytoolkit.googleapis.com/v1/accounts:update";
const RESET_PASSWORD_URL = "https://identitytoolkit.googleapis.com/v1/accounts:resetPassword";
const REFRESH_TOKEN_URL = "https://securetoken.googleapis.com/v1/token";
const GET_ACCOUNT_INFO_URL = "https://identitytoolkit.googleapis.com/v1/accounts:lookup";

export class ServerProxyAuthStrategy implements IAuthStrategy {

  readonly strategyType = AuthStrategy.SERVER_PROXY;

  constructor() {
    if (browser) {
      this.initializeAuth();
    }
  }

  private async initializeAuth() {
    try {
      await this.reload();
    } catch (error) {
      console.error("Auth proxy server initialization failed:", error);
    }
  }

  private _updateUser(firebaseUserData: any): AuthUser {
    userAuth.update((user: AuthUser) => {
      const now = Date.now();

      if (firebaseUserData.expiresIn) {
        const expirationTime = firebaseUserData.expiresIn ? now + firebaseUserData.expiresIn * 1000 : undefined;

        if (expirationTime) {
          firebaseUserData.expirationTime = expirationTime;
        }
      }

      try {
        const { quota_pool, token_usage } = JSON.parse(firebaseUserData.customAttributes);
        if (quota_pool) {
          firebaseUserData.quotaPool = quota_pool;
        }
        if (token_usage) {
          firebaseUserData.tokenUsage = token_usage;
        }

      } catch (error) {
        console.log("Parse quota pool failed:", error);
      }

      console.log("Update user(Server Proxy):", user, firebaseUserData, now, firebaseUserData.expiresIn);

      return {
        ...user,
        ...firebaseUserData,
      };
    });

    return get(userAuth);
  }

  supportAuthMethod(method: AuthMethod): boolean {
    return [
      AuthMethod.PASSWORD,
      AuthMethod.EMAIL_LINK,
    ].some(m => m === method);
  }

  async signUp(email: string, password: string): Promise<AuthResponse> {
    const emailValidation = InputValidator.validateEmail(email);
    if (!emailValidation.isValid) {
      return { success: false, toast: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
    }

    const passwordValidation = InputValidator.validatePassword(password);
    if (!passwordValidation.isValid) {
      return { success: false, toast: { code: "WEAK_PASSWORD", message: passwordValidation.errors.join("\n") || "密码强度不够" } };
    }

    try {
      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);

      const response = await fetch(`${API_BASE_URL}${SIGN_UP_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: cleanEmail, password, returnSecureToken: true }),
      });

      const data = await response.json();
      if (!response.ok) return { success: false, toast: this.parseFirebaseError(data.error) };

      const user = this._updateUser(data);
      await this.sendEmailVerification(user.idToken);

      return this._checkVerificationAndActivation(data, { code: "SUCCESS", message: "注册成功" });
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const emailValidation = InputValidator.validateEmail(email);
      if (!emailValidation.isValid) {
        return { success: false, toast: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
      }

      const passwordValidation = InputValidator.validatePassword(password);
      if (!passwordValidation.isValid) {
        return { success: false, toast: { code: "WEAK_PASSWORD", message: ["密码错误", ...passwordValidation.errors].join("\n") } };
      }

      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);

      const response = await fetch(`${API_BASE_URL}${SIGN_IN_PASSWORD_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: cleanEmail, password, returnSecureToken: true }),
      });

      const data = await response.json();
      if (!response.ok) {
        return { success: false, toast: this.parseFirebaseError(data.error) };
      }

      await this.reload(data.idToken);
      const user = this._updateUser(data);

      return this._checkVerificationAndActivation(user, { code: "SUCCESS", message: "登录成功" });

    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async signInWithGoogle(): Promise<AuthResponse> {
    // 服务端代理策略不支持Google登录，需要切换到Firebase SDK策略
    return {
      success: false,
      toast: {
        code: "UNSUPPORTED_OPERATION",
        message: "服务端代理模式不支持Google登录，请切换到直连模式"
      }
    };
  }

  async signOut(): Promise<AuthResponse> {
    try {
      const { auth } = fireapp();
      await firebaseSignOut(auth);
    } catch (error) {
      console.log("Sign out failed:", error);
    } finally {
      userAuth.set({} as AuthUser);

      console.log("Signed out, redirecting to login");
      if (browser) {
        return { success: true, toast: { code: "SUCCESS", message: "已退出登录" }, redirectUrl: "/login" };
      }
      return { success: true, toast: { code: "SUCCESS", message: "已退出登录" } };
    }
  }

  async sendOobcode(requestType: "EMAIL_SIGNIN" | "PASSWORD_RESET" | "VERIFY_EMAIL" | "VERIFY_AND_CHANGE_EMAIL", data: any) {
    const continueUrl = `${window.location.origin}${env.PUBLIC_GOOGLE_FIREBASE_AUTH_CONTINUE_URL ?? ""}`
    return fetch(`${API_BASE_URL}${SEND_OOB_CODE_URL}`, {
      method: "POST",
      headers: { "Content-Type": "application/json", "X-Firebase-Locale": navigator.language || "en" },
      body: JSON.stringify({ requestType, continueUrl, ...data }),
    });
  }

  async sendEmailVerification(idToken?: string): Promise<AuthResponse> {
    try {
      const _idToken = idToken ?? await this.idToken();
      if (_idToken) {
        const response = await this.sendOobcode("VERIFY_EMAIL", { idToken: _idToken });
        return { success: response.ok };
      }
      return { success: false, toast: { code: "NO_USER", message: "用户未登录" } };
    } catch (error) {
      console.error("发送验证邮件失败:", error);
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }
  async sendPasswordResetEmail(email: string): Promise<AuthResponse> {
    try {
      const emailValidation = InputValidator.validateEmail(email);
      if (!emailValidation.isValid) {
        return { success: false, toast: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
      }

      const response = await this.sendOobcode("PASSWORD_RESET", { email });
      return { success: response.ok };
    } catch (error) {
      console.error("发送重置密码邮件失败:", error);
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }
  async sendEmailLinkSignIn(email: string): Promise<AuthResponse> {
    try {
      const emailValidation = InputValidator.validateEmail(email);
      if (!emailValidation.isValid) {
        return { success: false, toast: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
      }

      const response = await this.sendOobcode("EMAIL_SIGNIN", { email });
      const data = await response.json();
      this._updateUser(data);
      return { success: response.ok };
    } catch (error) {
      console.error("发送邮箱链接登录邮件失败:", error);
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async handleAuthCallback(mode: string, oobCode: string, continueUrl?: string): Promise<AuthResponse> {
    switch (mode) {
      case "resetPassword":
        if (continueUrl) {
          return { success: true, redirectUrl: `/auth/reset-password?oobCode=${encodeURIComponent(oobCode)}&continueUrl=${encodeURIComponent(continueUrl)}` };
        } else {
          return { success: true, redirectUrl: `/auth/reset-password?oobCode=${encodeURIComponent(oobCode)}` };
        }
      case "signIn":
        return await this.handleEmailLinkSignIn(oobCode, continueUrl);
      case "verifyEmail":
        return await this.handleEmailVerification(oobCode, continueUrl);
      default:
        return { success: false, toast: { code: "INVALID_MODE", message: "无效的操作链接(mode)" }, redirectUrl: continueUrl };
    }
  }

  private async handleEmailLinkSignIn(oobCode: string, continueUrl?: string): Promise<AuthResponse> {
    try {
      const email = this.getCurrentUser()?.email;
      if (!email) {
        return { success: false, toast: { code: "MISSING_EMAIL", message: "需要邮箱地址才能完成登录" } };
      }

      const response = await fetch(`${API_BASE_URL}${SIGN_IN_EMAIL_LINK_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, oobCode }),
      });

      const data = await response.json();
      if (!response.ok) {
        return { success: false, toast: this.parseFirebaseError(data.error) };
      }

      await this.reload(data.idToken);
      const user = this._updateUser(data);

      return this._checkVerificationAndActivation(user, { code: "SUCCESS", message: "登录成功" }, continueUrl);
    } catch (error) {
      console.log("邮件链接登录失败:", error);
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  private async handleEmailVerification(oobCode: string, continueUrl?: string): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}${UPDATE_ACCOUNT_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ oobCode }),
      });

      const data = await response.json();
      if (!response.ok) {
        return { success: false, toast: this.parseFirebaseError(data.error) };
      }

      const _idToken = await this.idToken();
      if (_idToken) {
        await this.reload(data.idToken);
        const user = this._updateUser(data);
        return this._checkVerificationAndActivation(user, { code: "SUCCESS", message: "邮箱验证成功！" }, continueUrl);
      } else {
        return { success: true, toast: { code: "NO_USER", message: "验证成功, 请登录" }, redirectUrl: '/login' };
      }
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async handlePasswordReset(oobCode: string, newPassword: string): Promise<AuthResponse> {
    const passwordValidation = InputValidator.validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      return { success: false, toast: { code: "WEAK_PASSWORD", message: passwordValidation.errors.join("\n") || "密码强度不够" } };
    }

    try {
      const response = await fetch(`${API_BASE_URL}${RESET_PASSWORD_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ oobCode, newPassword }),
      });

      const data = await response.json();
      if (!response.ok) {
        return { success: false, toast: this.parseFirebaseError(data.error) };
      }
      return { success: true, toast: { code: "SUCCESS", message: "密码重置成功, 请重新登录！" }, redirectUrl: "/login" };
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async idToken(forceRefresh?: boolean): Promise<string> {
    const user = this.getCurrentUser();
    if (!user) return "";
    const _idToken = user.idToken;
    const expirationTime = user.expirationTime || Date.now();
    if (!forceRefresh && _idToken && Date.now() < expirationTime) {
      return _idToken;
    }
    try {
      const response = await fetch(`${API_BASE_URL}${REFRESH_TOKEN_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ grant_type: "refresh_token", refresh_token: user.refreshToken }),
      });

      const data = await response.json();
      if (!response.ok) {
        return "";
      }
      this._updateUser({
        idToken: data.id_token,
        refreshToken: data.refresh_token,
        expiresIn: data.expires_in,
      });
      return data.id_token;
    } catch (error) {
      console.log("Refresh token failed:", error);
      return "";
    }
  }

  async reload(idtoken?: string): Promise<AuthResponse> {
    const token = idtoken ?? await this.idToken();
    if (!token) {
      console.warn("用户未登录");
      return { success: false, toast: { code: "NO_USER", message: "用户未登录" } };
    }

    try {
      const response = await fetch(`${API_BASE_URL}${GET_ACCOUNT_INFO_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ idToken: token }),
      });

      const data = await response.json();
      if (!response.ok) {
        return { success: false, toast: this.parseFirebaseError(data) };
      }

      if (!data.users || 0 == data.users.length) {
        return { success: false, toast: this.parseFirebaseError(data) };
      }

      const user = this._updateUser(data.users[0]);
      return { success: true, user };
    } catch (error) {
      console.log("Reload user failed:", error);

      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  getCurrentUser(): AuthUser | null {
    const user = get(userAuth);
    return user && user.idToken ? user : null;
  }

  isAuthenticated(): boolean {
    return !!this.getCurrentUser()?.idToken;
  }
  isEmailVerified(): boolean {
    return !!this.getCurrentUser()?.emailVerified;
  }
  isActivated(): boolean {
    return !!this.getCurrentUser()?.quotaPool;
  }

  async isAvailable(): Promise<boolean> {
    try {
      // 检查代理服务是否可用
      await fetch(`${API_BASE_URL}${BASE_URL}`);

      // 即使返回错误，只要能连接到代理服务就说明可用
      return true;
    } catch (error) {
      console.log('Server proxy not available:', error);
      return false;
    }
  }
  async getHealthStatus(): Promise<{ healthy: boolean; latency?: number; }> {
    const startTime = Date.now();
    try {
      const available = await this.isAvailable();
      const latency = Date.now() - startTime;

      if (available) {
        // 额外检查网络连接
        return { healthy: true, latency };
      }

      return { healthy: false };
    } catch (error) {
      return { healthy: false };
    }
  }

  private _checkVerificationAndActivation(user: AuthUser, toast: AuthToast, continueUrl?: string): AuthResponse {
    if (!this.isEmailVerified()) {
      return { success: true, user, toast: { code: "EMAIL_NOT_VERIFIED", message: `${toast.message}: "邮箱未验证"` }, redirectUrl: "/auth/verify-email" };
    }
    if (!this.isActivated()) {
      return { success: true, user, toast: { code: "ACCOUNT_NOT_ACTIVATED", message: `${toast.message}: "账号未激活"` }, redirectUrl: "/auth/activate" };
    }
    return { success: true, user, toast, redirectUrl: continueUrl };
  }

  private parseFirebaseError(error: any): AuthToast {
    console.warn('auth-strategy-proxy.service parseFirebaseError:', error);

    const errorCode = error?.error?.message || error?.name || error?.message || "NETWORK_ERROR";
    const errorMessages: { [key: string]: string } = {
      "EMAIL_EXISTS": "该邮箱已被注册",
      "OPERATION_NOT_ALLOWED": "该操作不被允许",
      "TOO_MANY_ATTEMPTS_TRY_LATER": "尝试次数过多，请稍后再试",
      "EMAIL_NOT_FOUND": "邮箱不存在",
      "INVALID_PASSWORD": "密码错误",
      "USER_DISABLED": "用户账号已被禁用",
      "INVALID_EMAIL": "邮箱格式无效",
      "WEAK_PASSWORD": "密码强度不够",
      "MISSING_PASSWORD": "请输入密码",
      "INVALID_ID_TOKEN": "登录状态已过期，请重新登录",
      "USER_NOT_FOUND": "用户不存在",
      "CREDENTIAL_TOO_OLD_LOGIN_AGAIN": "登录状态已过期，请重新登录",
      "AbortError": "请求超时，请检查网络连接",
      "NETWORK_ERROR": "网络连接失败，请检查网络设置",
    };

    return {
      code: errorCode,
      message: errorMessages[errorCode] || `认证失败: ${errorCode}`
    };
  }
}
