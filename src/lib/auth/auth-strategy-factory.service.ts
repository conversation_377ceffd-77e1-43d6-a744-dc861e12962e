import { browser } from "$app/environment";
import { addToast } from "$lib/components/toast/toastStore";
import { createPersistedStore } from "../services/local.service";
import { networkDetector } from "./network-detector.service";
import type {
  IAuthStrategy,
  IAuthStrategyFactory,
  INetworkDetector,
  NetworkStatus,
  AuthStrategyConfig
} from "./auth-strategy.types";
import { AuthStrategy } from "./auth-strategy.types";
import { get } from "svelte/store";
import { FirebaseSDKAuthStrategy } from "./auth-strategy-firebase.service";
import { ServerProxyAuthStrategy } from "./auth-strategy-proxy.service";

export class AuthStrategyFactory implements IAuthStrategyFactory {
  private defaultStrategy = new FirebaseSDKAuthStrategy();

  private currentStrategy: IAuthStrategy | null = null;
  private strategies: Map<AuthStrategy, IAuthStrategy> = new Map();
  private networkDetector: INetworkDetector;
  private isMonitoring = false;

  // 持久化配置
  private configStore = createPersistedStore<AuthStrategyConfig>("authStrategyConfig", {
    strategy: AuthStrategy.HYBRID, // 默认使用 hybrid 代理
    fallbackStrategy: AuthStrategy.SERVER_PROXY,
    autoSwitch: false,
    checkInterval: 300000, // 5分钟
    timeout: 5000 // 5秒
  });

  // 持久化当前策略
  private currentStrategyStore = createPersistedStore<AuthStrategy>("currentAuthStrategy", AuthStrategy.HYBRID);

  constructor(networkDetectorInstance?: INetworkDetector) {
    this.networkDetector = networkDetectorInstance || networkDetector;

    if (browser) {
      this.initializeStrategies();
      this.loadCurrentStrategy();
      this.refreshAndRecommend();
    }
  }

  private initializeStrategies() {
    // 初始化所有可用的认证策略
    this.strategies.set(AuthStrategy.SERVER_PROXY, new ServerProxyAuthStrategy());
    this.strategies.set(AuthStrategy.FIREBASE_SDK, new FirebaseSDKAuthStrategy());
    this.networkDetector.setConfig({
      checkInterval: get(this.configStore).checkInterval,
      timeout: get(this.configStore).timeout
    });
  }

  private async loadCurrentStrategy() {
    const savedStrategy = get(this.currentStrategyStore);
    const config = get(this.configStore);

    if (savedStrategy === AuthStrategy.HYBRID) {
      // 自动选择最佳策略
      const networkStatus = await this.networkDetector.checkNetworkStatus();
      const recommendedStrategy = this.getRecommendedStrategy(networkStatus);
      await this.switchStrategy(recommendedStrategy);
    } else {
      // 使用保存的策略
      await this.switchStrategy(savedStrategy);
    }

    if (config.autoSwitch) {
      // 开始网络监控
      this.startNetworkMonitoring();
    }
  }

  createStrategy(strategy: AuthStrategy): IAuthStrategy {
    const strategyInstance = this.strategies.get(strategy);
    if (!strategyInstance) {
      throw new Error(`认证策略 ${strategy} 不存在`);
    }
    return strategyInstance;
  }

  getRecommendedStrategy(networkStatus: NetworkStatus): AuthStrategy {
    const config = get(this.configStore);

    if (!networkStatus.isOnline) {
      // 离线状态，无法使用任何策略
      return config.strategy;
    }

    if (networkStatus.canAccessFirebase && networkStatus.canAccessGoogle) {
      // 可以直接访问Firebase和Google服务，优先使用SDK
      return AuthStrategy.FIREBASE_SDK;
    } else {
      // 无法直接访问，使用服务端代理
      return AuthStrategy.SERVER_PROXY;
    }
  }

  async switchStrategy(newStrategy: AuthStrategy): Promise<void> {
    try {
      const strategy = this.createStrategy(newStrategy);

      this.currentStrategy = strategy;

      // 检查策略是否可用
      const isAvailable = await strategy.isAvailable();
      if (!isAvailable) {
        console.warn(`认证策略 ${newStrategy} 不可用，尝试回退策略`);

        const config = get(this.configStore);
        if (config.fallbackStrategy && config.fallbackStrategy !== newStrategy) {
          const fallbackStrategy = this.createStrategy(config.fallbackStrategy);
          const fallbackAvailable = await fallbackStrategy.isAvailable();

          if (fallbackAvailable) {
            this.currentStrategy = fallbackStrategy;
            this.currentStrategyStore.set(config.fallbackStrategy);
            addToast({
              type: "warning",
              message: `已切换到备用认证方式: ${this.getStrategyDisplayName(config.fallbackStrategy)}`
            });
            return;
          }
        }

        throw new Error(`所有认证策略都不可用`);
      }

      // 清理旧策略
      if (this.currentStrategy && this.currentStrategy !== strategy) {
        if ('destroy' in this.currentStrategy && typeof this.currentStrategy.destroy === 'function') {
          this.currentStrategy.destroy();
        }
      }

      this.currentStrategy = strategy;
      this.currentStrategyStore.set(newStrategy);

      console.log(`已切换到认证策略: ${newStrategy}`);
    } catch (error) {
      console.error('切换认证策略失败:', error);
      addToast({ type: "error", message: "认证策略切换失败" });
      throw error;
    }
  }

  getCurrentStrategy(): IAuthStrategy {
    if (!this.currentStrategy) {
      return this.defaultStrategy;
    }
    return this.currentStrategy;
  }

  getCurrentStrategyType(): AuthStrategy {
    return get(this.currentStrategyStore);
  }

  private startNetworkMonitoring() {
    console.warn("开始网络监控");

    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.networkDetector.startMonitoring(async (networkStatus) => {
      const config = get(this.configStore);
      if (!config.autoSwitch) return;

      const currentStrategyType = this.getCurrentStrategyType();
      const recommendedStrategy = this.getRecommendedStrategy(networkStatus);

      if (currentStrategyType !== recommendedStrategy) {
        console.log(`网络环境变化，建议切换认证策略: ${currentStrategyType} -> ${recommendedStrategy}`);

        try {
          await this.switchStrategy(recommendedStrategy);
          addToast({
            type: "info",
            message: `已自动切换到: ${this.getStrategyDisplayName(recommendedStrategy)}`
          });
        } catch (error) {
          console.error('自动切换认证策略失败:', error);
        }
      }
    });
  }

  stopNetworkMonitoring() {
    if (this.isMonitoring) {
      this.networkDetector.stopMonitoring();
      this.isMonitoring = false;
    }
  }

  // 配置管理
  updateConfig(config: Partial<AuthStrategyConfig>) {
    const currentConfig = get(this.configStore);
    const newConfig = { ...currentConfig, ...config };
    this.configStore.set(newConfig);
    this.networkDetector.setConfig({
      checkInterval: newConfig.checkInterval,
      timeout: newConfig.timeout
    });

    if (config.autoSwitch !== undefined) {
      if (config.autoSwitch) {
        this.startNetworkMonitoring();
      } else {
        this.stopNetworkMonitoring();
      }
    }
  }

  getConfig(): AuthStrategyConfig {
    return get(this.configStore);
  }

  // 获取策略健康状态
  async getStrategiesHealth(): Promise<{ [key in AuthStrategy]?: { healthy: boolean; latency?: number } }> {
    const results: { [key in AuthStrategy]?: { healthy: boolean; latency?: number } } = {};

    for (const [strategyType, strategy] of this.strategies) {
      try {
        results[strategyType] = await strategy.getHealthStatus();
      } catch (error) {
        results[strategyType] = { healthy: false };
      }
    }

    return results;
  }

  // 手动刷新网络状态并重新评估策略
  async refreshAndRecommend(): Promise<AuthStrategy> {
    console.log("刷新网络状态并重新评估策略");

    const networkStatus = await this.networkDetector.forceRefresh();
    const recommendedStrategy = this.getRecommendedStrategy(networkStatus);

    await this.switchStrategy(recommendedStrategy);

    return recommendedStrategy;
  }

  private getStrategyDisplayName(strategy: AuthStrategy): string {
    const names = {
      [AuthStrategy.FIREBASE_SDK]: "Firebase直连",
      [AuthStrategy.SERVER_PROXY]: "服务端代理",
      [AuthStrategy.HYBRID]: "混合模式"
    };
    return names[strategy] || strategy;
  }

  // 清理资源
  destroy() {
    this.stopNetworkMonitoring();

    // 清理所有策略
    for (const strategy of this.strategies.values()) {
      if ('destroy' in strategy && typeof strategy.destroy === 'function') {
        strategy.destroy();
      }
    }

    this.strategies.clear();
    this.currentStrategy = null;
  }
}

// 单例实例
export const authStrategyFactory = new AuthStrategyFactory();
