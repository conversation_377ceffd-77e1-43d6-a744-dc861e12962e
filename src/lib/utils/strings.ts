
// 模拟终端/等宽字体中的显示宽度
export const getDisplayWidth = (str: string, defaultSize?: number) => {
  let width = 0;
  for (const char of str) {
    const code = char.codePointAt(0)!;

    // 简化的东亚文字宽度判断
    if (
      (code >= 0x1100 && code <= 0x115f) || // 韩文字母
      (code >= 0x2e80 && code <= 0x9fff) || // CJK 统一汉字等
      (code >= 0xac00 && code <= 0xd7af) || // 韩文音节
      (code >= 0xf900 && code <= 0xfaff) || // CJK 兼容汉字
      (code >= 0xfe10 && code <= 0xfe19) || // 垂直形式
      (code >= 0xfe30 && code <= 0xfe6f) || // CJK 兼容形式
      (code >= 0xff00 && code <= 0xff60) || // 全角字符
      (code >= 0xffe0 && code <= 0xffe6) || // 全角符号
      (code >= 0x20000 && code <= 0x2fffd) || // CJK 扩展
      (code >= 0x30000 && code <= 0x3fffd) // CJK 扩展
    ) {
      width += 1.9; // 全角字符
    } // 窄字符
    else if (
      [
        "i",
        "j",
        "l",
        "t",
        "f",
        "r",
        "!",
        "|",
        ".",
        ",",
        ";",
        ":",
        "'",
      ].includes(char.toLowerCase())
    ) {
      width += 0.6; // 窄字符
    }
    // 宽字符
    else if (["m", "w", "M", "W", "@", "#", "%", "&"].includes(char)) {
      width += 1.4; // 宽字符
    }
    // 数字和标点
    else if (/[0-9]/.test(char)) {
      width += 0.9; // 数字稍窄
    }
    // 普通字符
    else {
      width += 1; // 标准宽度
    }
  }
  width = Math.ceil(width);
  
  return width == 0 ? defaultSize : width;
};