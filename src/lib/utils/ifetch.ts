// import HttpsProxyAgent from 'https-proxy-agent';

import { dev } from '$app/environment';

// src/lib/utils/ifetch.ts
export async function ifetch(input: string | URL | globalThis.Request, init?: RequestInit) {
    if (dev) {
        console.log('ifetch proxy', input);

        // 仅在开发环境中使用 node-fetch 走代理
        const fetch = (await import('node-fetch')).default;
        const { HttpsProxyAgent } = (await import('https-proxy-agent')).default;

        // 禁用 TLS 证书验证（仅开发环境）
        // process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

        const proxyAgent = new HttpsProxyAgent({
            host: "127.0.0.1",
            port: "1080",
            protocol: 'http:',
            rejectUnauthorized: false,
        });
        const proxyInit: any = init
            ? { ...init, agent: proxyAgent, window: undefined }
            : { agent: proxyAgent };
        return fetch(input.toString(), proxyInit);
    } else {
        console.log('ifetch local', input);
        // 生产环境使用默认 fetch（如直接向正式 API 请求）
        return fetch(input, init);
    }
}
