/**
 * 激活码工具函数
 * 提供激活码相关的业务逻辑处理
 */

import {
  ActivationCodeType,
  type ActivationCode,
  ActivationCodeStatus,
  type QuotaAmount
} from '$lib/types/activation-code.types';

/**
 * 生成激活码
 * @param length 激活码长度，默认20位
 * @returns 格式化的激活码 (XXXX-XXXX-XXXX-XXXX-XXXX)
 */
export function generateActivationCode(length: number = 20): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  // 每4个字符添加一个分隔符
  return result.replace(/(.{4})/g, '$1-').replace(/-$/, '');
}

/**
 * 验证激活码格式
 * @param code 激活码
 * @returns 是否有效
 */
export function validateActivationCodeFormat(code: string): boolean {
  // 移除分隔符
  const cleanCode = code.replace(/[-\s]/g, '');

  // 检查长度和字符
  return /^[A-Za-z0-9]{1,20}$/.test(cleanCode);
}

/**
 * 格式化激活码显示
 * @param code 原始激活码
 * @returns 格式化后的激活码
 */
export function formatActivationCode(code: string): string {
  // 移除所有非字母数字字符
  const cleaned = code.replace(/[^A-Za-z0-9]/g, '');

  // 限制长度
  const limited = cleaned.slice(0, 20);

  // 每4个字符添加一个分隔符
  return limited.replace(/(.{4})/g, '$1-').replace(/-$/, '');
}

/**
 * 计算激活码状态
 * @param activationCode 激活码对象
 * @returns 激活码状态
 */
export function calculateActivationCodeStatus(activationCode: ActivationCode): ActivationCodeStatus {
  const now = Date.now();

  // 如果已经被激活（有激活邮箱）
  if (activationCode.activatedEmail) {
    return ActivationCodeStatus.USED;
  }

  // 检查是否过期（创建时间 + 过期月数）
  const expireTime = activationCode.createdAt + (activationCode.expireAt * 30 * 24 * 60 * 60 * 1000);
  if (now > expireTime) {
    return ActivationCodeStatus.EXPIRED;
  }

  return ActivationCodeStatus.ACTIVE;
}

/**
 * 计算剩余有效天数
 * @param activationCode 激活码对象
 * @returns 剩余天数，-1表示已过期
 */
export function calculateRemainingDays(activationCode: ActivationCode): number {
  const now = Date.now();

  // 如果已经被使用，返回0
  if (activationCode.activatedEmail) {
    return 0;
  }

  // 计算过期时间
  const expireTime = activationCode.expireAt;

  if (now > expireTime) {
    return -1; // 已过期
  }

  // 计算剩余天数
  const remainingMs = expireTime - now;
  return Math.ceil(remainingMs / (24 * 60 * 60 * 1000));
}

/**
 * 格式化日期显示
 * @param timestamp 时间戳
 * @returns 格式化的日期字符串
 */
export function formatDate(timestamp: number | null): string {
  if (!timestamp) return '未知';

  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * 格式化相对时间
 * @param timestamp 时间戳
 * @returns 相对时间字符串
 */
export function formatRelativeTime(timestamp: number | null): string {
  if (!timestamp) return '未知';

  const now = Date.now();
  const diff = now - timestamp;

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const month = 30 * day;

  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`;
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`;
  } else if (diff < month) {
    return `${Math.floor(diff / day)}天前`;
  } else {
    return formatDate(timestamp);
  }
}

/**
 * 获取激活码类型显示文本
 * @param type 激活码类型
 * @returns 显示文本
 */
export function getActivationCodeTypeText(type: ActivationCodeType): string {
  switch (type) {
    case ActivationCodeType.BONUS:
      return '奖励额度';
    case ActivationCodeType.PLAN:
      return '订阅计划';
    default:
      return '未知类型';
  }
}

/**
 * 获取激活码是否为非卖品颜色类
 * @returns Tailwind CSS 颜色类
 */
export function getActivationCodeNotForSaleColor(notForSale?: boolean): string {
  return notForSale ? 'bg-red-100 text-red-800' : 'bg-cyan-100 text-cyan-800';
}

/**
 * 获取激活码是否为非卖品显示文本
 */
export function getActivationCodeNotForSaleText(notForSale?: boolean): string {
  return notForSale ? "非卖品" : "卖品";
}

/**
 * 获取激活码状态显示文本
 * @param status 激活码状态
 * @returns 显示文本
 */
export function getActivationCodeStatusText(status: ActivationCodeStatus): string {
  switch (status) {
    case ActivationCodeStatus.ACTIVE:
      return '可用';
    case ActivationCodeStatus.USED:
      return '已使用';
    case ActivationCodeStatus.EXPIRED:
      return '已过期';
    default:
      return '未知状态';
  }
}

/**
 * 获取激活码状态颜色类
 * @param status 激活码状态
 * @returns Tailwind CSS 颜色类
 */
export function getActivationCodeStatusColor(status: ActivationCodeStatus): string {
  switch (status) {
    case ActivationCodeStatus.ACTIVE:
      return 'bg-green-100 text-green-800';
    case ActivationCodeStatus.USED:
      return 'bg-gray-100 text-gray-800';
    case ActivationCodeStatus.EXPIRED:
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * 获取激活码类型颜色类
 * @param type 激活码类型
 * @returns Tailwind CSS 颜色类
 */
export function getActivationCodeTypeColor(type: ActivationCodeType): string {
  switch (type) {
    case ActivationCodeType.BONUS:
      return 'bg-purple-100 text-purple-800';
    case ActivationCodeType.PLAN:
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * 验证额度对象
 * @param amount 额度对象
 * @returns 是否有效
 */
export function validateQuotaAmount(amount: QuotaAmount): boolean {
  return (
    typeof amount.agent === 'number' &&
    typeof amount.completion === 'number' &&
    amount.agent >= 0 &&
    amount.completion >= 0
  );
}

/**
 * 格式化额度显示
 * @param amount 额度对象
 * @returns 格式化的额度字符串
 */
export function formatQuotaAmount(amount: QuotaAmount): string {
  return `代理: ${amount.agent}, 补全: ${amount.completion}`;
}

/**
 * 创建默认额度
 * @returns 默认额度对象
 */
export function createDefaultQuotaAmount(): QuotaAmount {
  return {
    agent: 30,
    completion: 200
  };
}

/**
 * 计算订阅周期（方案4：按使用量+结算周期）
 * 配额过期时间计算方式:
   逻辑：订阅周期只是发额度的时间窗口。每月额度在「周期开始日」刷新，不受自然月约束。
   例子：8月23日订阅 → 每月 23 日刷新额度。12 个月后 8月22日到期。
   特点：特别适合 SaaS（API 调用、Tokens、存储空间），用户会更清楚“每月给我一份额度”。
 * @param {Date} startAt 订阅开始时间
 * @param {number} validityMonths 有效月数
 * @returns {{ startAt: Date, endAt: Date, cycleDay: number }}
 */
export function calcSubscriptionPeriod(startAt: Date, validityMonths: number): { endAt: Date; cycleDay: number; } {
  // 复制一份再加上有效月份
  const endAt = new Date(startAt);
  endAt.setMonth(endAt.getMonth() + validityMonths);

  // 减去1天：8月23日订阅12个月 -> 结束 8月22日
  endAt.setDate(endAt.getDate() - 1);

  // 有效时间截止于最后一天的 23:59:59
  endAt.setHours(23);
  endAt.setMinutes(59);
  endAt.setSeconds(59);

  // 每月刷新日（绑定开始日期的 day）
  const cycleDay = startAt.getDate();

  return { endAt, cycleDay };
}