import { browser } from "$app/environment";

// 频率限制器
class RateLimiter {
  private attempts: Map<string, { count: number; lastAttempt: number }> = new Map();
  private maxAttempts: number;
  private windowMs: number;

  constructor(maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isAllowed(key: string): boolean {
    if (!browser) return true; // 服务端不限制

    const now = Date.now();
    const attempt = this.attempts.get(key);

    if (!attempt) {
      this.attempts.set(key, { count: 1, lastAttempt: now });
      return true;
    }

    // 检查时间窗口是否已过期
    if (now - attempt.lastAttempt > this.windowMs) {
      this.attempts.set(key, { count: 1, lastAttempt: now });
      return true;
    }

    // 增加尝试次数
    attempt.count++;
    attempt.lastAttempt = now;

    return attempt.count <= this.maxAttempts;
  }

  getRemainingTime(key: string): number {
    if (!browser) return 0;

    const attempt = this.attempts.get(key);
    if (!attempt) return 0;

    const now = Date.now();
    const timeLeft = this.windowMs - (now - attempt.lastAttempt);
    return Math.max(0, timeLeft);
  }

  reset(key: string): void {
    this.attempts.delete(key);
  }
}

// 输入验证和清理
export class InputValidator {
  // 邮箱验证
  static validateEmail(email: string): { isValid: boolean; error?: string } {
    if (!email || typeof email !== 'string') {
      return { isValid: false, error: '邮箱地址不能为空' };
    }

    const trimmed = email.trim();
    if (trimmed.length === 0) {
      return { isValid: false, error: '邮箱地址不能为空' };
    }

    if (trimmed.length > 125) {
      return { isValid: false, error: '邮箱地址过长' };
    }

    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    if (!emailRegex.test(trimmed)) {
      return { isValid: false, error: '邮箱地址格式无效' };
    }

    return { isValid: true };
  }

  // 密码验证
  static validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!password || typeof password !== 'string') {
      errors.push('密码不能为空');
      return { isValid: false, errors };
    }

    if (password.length < 6) {
      errors.push('密码至少需要6个字符');
    }

    if (password.length > 128) {
      errors.push('密码不能超过128个字符');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('建议包含大写字母');
    }

    if (!/\d/.test(password)) {
      errors.push('建议包含数字');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('建议包含特殊字符, [!@#$%^&*(),.?":{}|<>]');
    }

    // 检查常见弱密码
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];

    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('密码过于简单，请使用更复杂的密码');
    }

    return { isValid: errors.length === 0, errors };
  }

  // 激活码验证
  static validateActivationCode(code: string): { isValid: boolean; error?: string } {
    if (!code || typeof code !== 'string') {
      return { isValid: false, error: '激活码不能为空' };
    }

    const cleaned = code.replace(/[^A-Za-z0-9]/g, '');

    if (cleaned.length < 6 || cleaned.length > 20) {
      return { isValid: false, error: '激活码长度无效' };
    }

    if (!/^[A-Za-z0-9]+$/.test(cleaned)) {
      return { isValid: false, error: '激活码只能包含字母和数字' };
    }

    return { isValid: true };
  }

  // HTML清理（防止XSS）
  static sanitizeHtml(input: string): string {
    if (!input || typeof input !== 'string') return '';

    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  // 通用字符串清理
  static sanitizeString(input: string, maxLength: number = 1000): string {
    if (!input || typeof input !== 'string') return '';

    return input
      .trim()
      .slice(0, maxLength)
      .replace(/[\x00-\x1F\x7F]/g, ''); // 移除控制字符
  }
}

// 创建频率限制器实例
export const authRateLimiter = new RateLimiter(5, 15 * 60 * 1000); // 15分钟内最多5次尝试
export const emailRateLimiter = new RateLimiter(3, 60 * 60 * 1000); // 1小时内最多3次邮件发送

// CSRF保护（简单实现）
export class CSRFProtection {
  private static token: string | null = null;

  static generateToken(): string {
    if (!browser) return '';

    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    const token = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');

    this.token = token;
    sessionStorage.setItem('csrf_token', token);

    return token;
  }

  static getToken(): string | null {
    if (!browser) return null;

    if (!this.token) {
      this.token = sessionStorage.getItem('csrf_token');
    }

    return this.token;
  }

  static validateToken(token: string): boolean {
    if (!browser) return true;

    const storedToken = this.getToken();
    return storedToken !== null && storedToken === token;
  }

  static clearToken(): void {
    if (!browser) return;

    this.token = null;
    sessionStorage.removeItem('csrf_token');
  }
}