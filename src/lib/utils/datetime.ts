import moment from 'moment';

export function getRecentTimeRange(period: 'day' | 'week' | 'month' | 'year', n: number = 1) {
    let timeAgo: number;

    if (period === 'day') {
        timeAgo = Date.now() - (n * 24 * 60 * 60 * 1000); // n天前的时间戳
    } else if (period === 'week') {
        timeAgo = Date.now() - (n * 7 * 24 * 60 * 60 * 1000); // n周前的时间戳
    } else if (period === 'month') {
        const now = new Date();
        now.setMonth(now.getMonth() - n); // 大约n个月前的时间戳 (30天)
        timeAgo = now.getTime();
    } else { // 'month'
        const now = new Date();
        now.setUTCFullYear(now.getUTCFullYear() - n); // 大约n年前的时间戳 (30天)
        timeAgo = now.getTime();
    }
    return timeAgo;
}

export function getDateString(unit: 'day' | 'week' | 'month' | 'year', n: number = 1) {
    if (unit === 'day') {
        return moment().subtract(n, 'days').format('YYYY-MM-DD');
    } else if (unit === 'week') {
        return moment().subtract(n, 'weeks').format('YYYY-WW');
    } else if (unit === 'month') {
        return moment().subtract(n, 'months').format('YYYY-MM');
    } else { // 'year'
        return moment().subtract(n, 'years').format('YYYY');
    }
}

// 辅助函数：根据日期字符串和单位计算时间戳范围
// dateString:
// 日期 YYYY-MM-DD
// 周 YYYY-WW
// 月 YYYY-MM
// 年 YYYY
export function getTimeRange(dateString: string, unit: 'day' | 'week' | 'month' | 'year'): { start: number; end: number; } | null {
    let startDate: moment.Moment;
    let endDate: moment.Moment;

    switch (unit) {
        case 'day':
            startDate = moment(dateString).startOf('day');
            endDate = moment(dateString).endOf('day');
            break;
        case 'week':
            startDate = moment(dateString).startOf('isoWeek'); // ISO 周 (周一为一周开始)
            endDate = moment(dateString).endOf('isoWeek');
            break;
        case 'month':
            startDate = moment(dateString).startOf('month');
            endDate = moment(dateString).endOf('month');
            break;
        case 'year':
            startDate = moment(dateString).startOf('year');
            endDate = moment(dateString).endOf('year');
            break;
        default:
            console.error("Invalid time unit:", unit);
            return null;
    }

    // 检查日期是否有效
    if (!startDate.isValid()) {
        console.error("Invalid date string:", dateString);
        return null;
    }

    return {
        start: startDate.valueOf(), // valueOf() 获取时间戳 (毫秒)
        end: endDate.valueOf()
    };
}
