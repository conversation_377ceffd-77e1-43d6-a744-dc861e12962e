export function toMarkdown(data: any, headingLevel = 2, title = ''): string {
  const indent = '#'.repeat(headingLevel);
  let md = '';

  if (title) {
    md += `\n${indent} ${title}\n`;
  }

  if (Array.isArray(data)) {
    data.forEach((item, i) => {
      md += toMarkdown(item, headingLevel + 1, `${title || 'Item'} ${i + 1}`);
    });
  } else if (typeof data === 'object' && data !== null) {
    for (const key in data) {
      const value = data[key];
      if (typeof value === 'object' && value !== null) {
        md += toMarkdown(value, headingLevel + 1, key);
      } else {
        md += `- ${key}: ${value}\n`;
      }
    }
  } else {
    md += `- ${title}: ${data}\n`;
  }

  return md;
}
