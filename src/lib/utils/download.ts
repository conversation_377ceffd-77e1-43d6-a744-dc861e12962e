
// 导出为JSON
function downloadJSONFile(filename: string, obj: any) {
  const dataStr = JSON.stringify(obj, null, 2);
  const dataBlob = new Blob([dataStr], { type: "application/json" });
  const url = URL.createObjectURL(dataBlob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename
    ? `${filename.trim()}.json`
    : "object.json";
  link.click();
  URL.revokeObjectURL(url);
}
