function setSelectionRange(node: HTMLElement, select: { start: number, end: number }) {
  // 判断 node 是否为 可输入的元素, 然后设置光标
  if (node instanceof HTMLInputElement || node instanceof HTMLTextAreaElement) {
    setTimeout(() => {
      node.setSelectionRange(select.start, select.end);
    }, 50);
  }
}

export function selectRange(node: HTMLElement, select: { start: number, end: number } | null) {
  if (select) {
    setSelectionRange(node, select);
  }

  return {
    update(select: { start: number, end: number } | null) {
      if (select) {
        setSelectionRange(node, select);
      }
    }
  };
}