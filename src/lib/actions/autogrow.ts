
// 自动设置高度变化（仅限于输入时-已解决：支持各种场景）
export function autogrow(node: HTMLTextAreaElement, value: string) {
  function resize() {
    node.style.height = "auto";
    node.style.height = node.scrollHeight + "px";
  }

  if (value != node.value) {
    node.value = value;
  }
  resize(); // 初始化时执行一次

  node.addEventListener("input", resize);
  return {
    update(newValue: string) {
      if (newValue != node.value) {
        node.value = newValue;
      }
      resize();
    },
    destroy() {
      node.removeEventListener("input", resize);
    },
  };
}