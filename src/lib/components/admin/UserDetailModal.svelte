<script lang="ts">

  import { authService } from "$lib/services/auth.service";
  import { addToast } from "$lib/components/toast/toastStore";
  import { <PERSON><PERSON>, Card, Badge, Loading } from "$lib/components/ui";
  import {
    X,
    User,
    Activity,
    Coins,
    Settings
  } from "@lucide/svelte";

  // 导入类型
  import type {
    UserQuotaInfo,
    UserUsageStats,
    TokenUsageRecord
  } from "$lib/types/user-quota.types";
  import { QuotaOperationType } from "$lib/types/user-quota.types";
  import { QuotaType } from "$lib/types/activation-code.types";

  // Props
  interface Props {
    user: UserQuotaInfo;
    show: boolean;
    onClose: () => void;
  }

  let { user, show, onClose }: Props = $props();

  // 状态管理
  let isLoading = $state(false);
  let userStats: UserUsageStats | null = $state(null);
  let recentRecords: TokenUsageRecord[] = $state([]);
  let showQuotaOperationModal = $state(false);

  // 配额操作状态
  let operationType = $state<QuotaOperationType>(QuotaOperationType.ADD);
  let quotaType = $state<QuotaType>(QuotaType.AGENT);
  let operationAmount = $state(10);
  let operationReason = $state("");

  /**
   * 获取用户详细信息
   */
  async function fetchUserDetails() {
    if (!user?.uid) return;

    try {
      isLoading = true;
      const token = await authService.idToken();
      
      const response = await fetch(`/api/admin/users/${user.uid}`, {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json"
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        userStats = result.data.usageStats;
        recentRecords = result.data.recentRecords || [];
      } else {
        throw new Error(result.error || "获取用户详细信息失败");
      }
    } catch (error: any) {
      console.error("Error fetching user details:", error);
      addToast({
        type: "error",
        message: error.message || "获取用户详细信息失败"
      });
    } finally {
      isLoading = false;
    }
  }

  /**
   * 执行配额操作
   */
  async function executeQuotaOperation() {
    if (!user?.uid) return;

    try {
      const token = await authService.idToken();
      
      const response = await fetch(`/api/admin/users/${user.uid}/quota`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          operation: operationType,
          quotaType: quotaType,
          amount: operationType === "add" || operationType === "subtract" ? operationAmount : undefined,
          reason: operationReason
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        addToast({
          type: "success",
          message: result.message || "配额操作成功"
        });
        
        // 关闭模态框并刷新数据
        showQuotaOperationModal = false;
        await fetchUserDetails();
        
        // 通知父组件刷新用户列表
        // 这里可以通过事件或回调来实现
      } else {
        throw new Error(result.error || "配额操作失败");
      }
    } catch (error: any) {
      console.error("Error executing quota operation:", error);
      addToast({
        type: "error",
        message: error.message || "配额操作失败"
      });
    }
  }

  /**
   * 格式化日期
   */
  function formatDate(timestamp: number | string): string {
    const date = typeof timestamp === 'number' ? new Date(timestamp) : new Date(timestamp);
    return date.toLocaleString("zh-CN");
  }

  /**
   * 格式化相对时间
   */
  function formatRelativeTime(timestamp: number | string): string {
    const now = new Date();
    const date = typeof timestamp === 'number' ? new Date(timestamp) : new Date(timestamp);
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return "今天";
    if (diffDays === 1) return "昨天";
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`;
    return `${Math.floor(diffDays / 365)}年前`;
  }

  /**
   * 获取配额类型显示文本
   */
  function getQuotaTypeText(type: QuotaType | string): string {
    return type === QuotaType.AGENT || type === "agent" ? "AI代理" : "文本补全";
  }

  /**
   * 获取操作类型显示文本
   */
  function getOperationTypeText(type: QuotaOperationType): string {
    switch (type) {
      case "add": return "增加配额";
      case "subtract": return "减少配额";
      case "reset": return "重置使用";
      case "expire": return "过期配额";
      default: return type;
    }
  }

  // 监听用户变化，重新获取数据
  $effect(() => {
    if (show && user?.uid) {
      fetchUserDetails();
    }
  });
</script>

{#if show}
  <!-- 模态框背景 -->
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <!-- 模态框内容 -->
    <div class="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
      <!-- 模态框头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0 h-12 w-12">
            {#if user.photoURL}
              <img class="h-12 w-12 rounded-full" src={user.photoURL} alt="" />
            {:else}
              <div class="h-12 w-12 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 flex items-center justify-center">
                <span class="text-white font-medium text-lg">
                  {user.email.charAt(0).toUpperCase()}
                </span>
              </div>
            {/if}
          </div>
          <div>
            <h2 class="text-2xl font-bold text-gray-900">
              {user.displayName || user.email.split("@")[0]}
            </h2>
            <p class="text-gray-600">{user.email}</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-2">
          <Button
            variant="primary"
            size="sm"
            onclick={() => (showQuotaOperationModal = true)}
            class="bg-gradient-to-r from-purple-600 to-blue-600"
          >
            <Settings class="w-4 h-4 mr-2" />
            <span class="hidden sm:block">配额操作</span>
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onclick={onClose}
            class="text-gray-600 hover:text-gray-900"
          >
            <X class="w-5 h-5" />
          </Button>
        </div>
      </div>

      <!-- 模态框内容区域 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
        {#if isLoading}
          <div class="flex items-center justify-center py-12">
            <Loading text="加载用户详情中..." size="lg" />
          </div>
        {:else}
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 左侧：用户基本信息和配额状态 -->
            <div class="space-y-6">
              <!-- 基本信息 -->
              <Card>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <User class="w-5 h-5 mr-2" />
                  基本信息
                </h3>
                
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <span class="text-gray-600">用户ID</span>
                    <span class="font-mono text-sm">{user.uid}</span>
                  </div>
                  
                  <div class="flex items-center justify-between">
                    <span class="text-gray-600">邮箱验证</span>
                    <Badge class={user.emailVerified ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                      {user.emailVerified ? "已验证" : "未验证"}
                    </Badge>
                  </div>
                  
                  <div class="flex items-center justify-between">
                    <span class="text-gray-600">账户状态</span>
                    <Badge class={user.disabled ? "bg-red-100 text-red-800" : "bg-green-100 text-green-800"}>
                      {user.disabled ? "已禁用" : "正常"}
                    </Badge>
                  </div>
                  
                  <div class="flex items-center justify-between">
                    <span class="text-gray-600">注册时间</span>
                    <span class="text-sm">{formatDate(user.createdAt || "")}</span>
                  </div>
                  
                  <div class="flex items-center justify-between">
                    <span class="text-gray-600">最后登录</span>
                    <span class="text-sm">{user.lastLoginAt ? formatDate(user.lastLoginAt) : "从未登录"}</span>
                  </div>
                </div>
              </Card>

              <!-- 配额详情 -->
              <Card>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Coins class="w-5 h-5 mr-2" />
                  配额详情
                </h3>

                <div class="space-y-4">
                  <!-- 总配额概览 -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                      <div class="text-sm text-blue-600 font-medium">AI代理</div>
                      <div class="text-2xl font-bold text-blue-900">
                        {user.totalQuota.agent.remaining}
                      </div>
                      <div class="text-xs text-blue-600">
                        总计 {user.totalQuota.agent.total}
                      </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg">
                      <div class="text-sm text-green-600 font-medium">文本补全</div>
                      <div class="text-2xl font-bold text-green-900">
                        {user.totalQuota.completion.remaining}
                      </div>
                      <div class="text-xs text-green-600">
                        总计 {user.totalQuota.completion.total}
                      </div>
                    </div>
                  </div>

                  <!-- 配额池详情 -->
                  {#if user.quotaPool.length > 0}
                    <div>
                      <h4 class="text-sm font-medium text-gray-700 mb-2">配额池</h4>
                      <div class="space-y-2">
                        {#each user.quotaPool as quota}
                          <div class="bg-gray-50 p-3 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                              <Badge class={quota.type === "plan" ? "bg-purple-100 text-purple-800" : "bg-orange-100 text-orange-800"}>
                                {quota.type === "plan" ? "订阅计划" : "奖励额度"}
                              </Badge>
                              <span class="text-xs text-gray-500">
                                {formatRelativeTime(quota.created_at)}
                              </span>
                            </div>

                            <div class="grid grid-cols-2 gap-2 text-xs">
                              <div>
                                <span class="text-gray-600">AI代理:</span>
                                <span class="font-medium">{quota.used.agent}/{quota.amount.agent}</span>
                              </div>
                              <div>
                                <span class="text-gray-600">文本:</span>
                                <span class="font-medium">{quota.used.completion}/{quota.amount.completion}</span>
                              </div>
                            </div>
                          </div>
                        {/each}
                      </div>
                    </div>
                  {:else}
                    <div class="text-center py-4 text-gray-500">
                      暂无配额
                    </div>
                  {/if}
                </div>
              </Card>
            </div>

            <!-- 右侧：使用统计和记录 -->
            <div class="space-y-6">
              <!-- 使用统计 -->
              {#if userStats}
                <Card>
                  <h3 class="text-typent-semibold text-gray-900 mb-4 flex items-center">
                    <Activity class="w-5 h-5 mr-2" />
                    使用统计
                  </h3>

                  <div class="space-y-4">
                    <!-- 总使用量 -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div class="bg-indigo-50 p-4 rounded-lg">
                        <div class="text-sm text-indigo-600 font-medium">总使用次数</div>
                        <div class="text-2xl font-bold text-indigo-900">
                          {userStats.totalUsage.agent + userStats.totalUsage.completion}
                        </div>
                        <div class="text-xs text-indigo-600">
                          AI: {userStats.totalUsage.agent} | 文本: {userStats.totalUsage.completion}
                        </div>
                      </div>

                      <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="text-sm text-purple-600 font-medium">Token消费</div>
                        <div class="text-2xl font-bold text-purple-900">
                          {userStats.tokenStats.totalTokens.toLocaleString()}
                        </div>
                        <div class="text-xs text-purple-600">
                          (AI) 提示: {userStats.tokenStats.agent.promptTokens} 
                          | 完成: {userStats.tokenStats.agent.completionTokens}
                          | 总计: {userStats.tokenStats.agent.totalTokens}
                        </div>
                        <div class="text-xs text-purple-600">
                          (文本) 提示: {userStats.tokenStats.completion.promptTokens} 
                          | 完成: {userStats.tokenStats.completion.completionTokens}
                          | 总计: {userStats.tokenStats.completion.totalTokens}
                        </div>
                      </div>
                    </div>

                    <!-- 最近使用趋势 -->
                    {#if userStats.dailyUsage.length > 0}
                      <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-2">最近7天使用量</h4>
                        <div class="space-y-1">
                          {#each userStats.dailyUsage.slice(0, 7) as daily}
                            <div class="flex items-center justify-between text-sm">
                              <span class="text-gray-600">{daily.date}</span>
                              <div class="flex items-center space-x-2">
                                {#each daily.usage as item}
                                  <span class="text-green-600">{getQuotaTypeText(item.model)}: {item.count}</span>
                                {/each}
                                <span class="text-purple-600">Token: {daily.tokens}</span>
                              </div>
                            </div>
                          {/each}
                        </div>
                      </div>
                    {/if}
                  </div>
                </Card>
              {/if}

              <!-- 最近使用记录 -->
              <Card>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Activity class="w-5 h-5 mr-2" />
                  最近使用记录
                </h3>

                {#if recentRecords.length > 0}
                  <div class="space-y-3 max-h-64 overflow-y-auto">
                    {#each recentRecords as record}
                      <div class="bg-gray-50 p-3 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                          <Badge variant="other" class={record.model === QuotaType.AGENT ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800"}>
                            {getQuotaTypeText(record.model)}
                          </Badge>
                          <span class="text-xs text-gray-500">
                            {formatRelativeTime(record.createdAt)}
                          </span>
                        </div>

                        <div class="text-sm text-gray-600">
                          <div>使用次数: {record.used}</div>
                          {#if record.tokenUsage.length > 0}
                            <div>Token消费: {record.tokenUsage[0].total_tokens || 0}</div>
                          {/if}
                          <div class="text-xs text-gray-500 mt-1">
                            配额来源: {record.quotaId}
                          </div>
                        </div>
                      </div>
                    {/each}
                  </div>
                {:else}
                  <div class="text-center py-4 text-gray-500">
                    暂无使用记录
                  </div>
                {/if}
              </Card>
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}

<!-- 配额操作模态框 -->
{#if showQuotaOperationModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-md w-full">
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">配额操作</h3>
        <Button
          variant="ghost"
          size="sm"
          onclick={() => (showQuotaOperationModal = false)}
          class="text-gray-600 hover:text-gray-900"
        >
          <X class="w-5 h-5" />
        </Button>
      </div>

      <div class="p-6">
        <form
          onsubmit={(e) => {
            e.preventDefault();
            executeQuotaOperation();
          }}
          class="space-y-4"
        >
          <!-- 操作类型 -->
          <div>
            <label for="operation-type" class="block text-sm font-medium text-gray-700 mb-2">
              操作类型
            </label>
            <select
              id="operation-type"
              bind:value={operationType}
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="add">增加配额</option>
              <option value="subtract">减少配额</option>
              <option value="reset">重置使用</option>
              <option value="expire">过期配额</option>
            </select>
          </div>

          <!-- 配额类型 -->
          <div>
            <label for="quota-type" class="block text-sm font-medium text-gray-700 mb-2">
              配额类型
            </label>
            <select
              id="quota-type"
              bind:value={quotaType}
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={QuotaType.AGENT}>AI代理</option>
              <option value={QuotaType.COMPLETION}>文本补全</option>
            </select>
          </div>

          <!-- 操作数量 -->
          {#if operationType === "add" || operationType === "subtract"}
            <div>
              <label for="operation-amount" class="block text-sm font-medium text-gray-700 mb-2">
                数量
              </label>
              <input
                id="operation-amount"
                type="number"
                bind:value={operationAmount}
                min="1"
                max="10000"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          {/if}

          <!-- 操作原因 -->
          <div>
            <label for="operation-reason" class="block text-sm font-medium text-gray-700 mb-2">
              操作原因（可选）
            </label>
            <textarea
              id="operation-reason"
              bind:value={operationReason}
              rows="3"
              placeholder="请输入操作原因..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            ></textarea>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onclick={() => (showQuotaOperationModal = false)}
            >
              取消
            </Button>

            <Button
              type="submit"
              variant="primary"
              class="bg-gradient-to-r from-purple-600 to-blue-600"
            >
              {getOperationTypeText(operationType)}
            </Button>
          </div>
        </form>
      </div>
    </div>
  </div>
{/if}
