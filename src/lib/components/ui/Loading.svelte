<script lang="ts">
  interface Props {
    size?: "sm" | "md" | "lg" | "xl";
    variant?: "spinner" | "dots" | "pulse";
    text?: string;
    fullScreen?: boolean;
    class?: string;
  }

  let {
    size = "md",
    variant = "spinner",
    text,
    fullScreen = false,
    class: className = ""
  }: Props = $props();

  // 尺寸样式
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
    xl: "w-12 h-12"
  };

  // 文本尺寸
  const textSizeClasses = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg",
    xl: "text-xl"
  };
</script>

{#if fullScreen}
  <div class="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
    <div class="flex flex-col items-center space-y-4">
      {#if variant === "spinner"}
        <div class={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`}></div>
      {:else if variant === "dots"}
        <div class="flex space-x-1">
          <div class={`bg-blue-600 rounded-full animate-bounce ${sizeClasses[size]}`} style="animation-delay: 0ms"></div>
          <div class={`bg-blue-600 rounded-full animate-bounce ${sizeClasses[size]}`} style="animation-delay: 150ms"></div>
          <div class={`bg-blue-600 rounded-full animate-bounce ${sizeClasses[size]}`} style="animation-delay: 300ms"></div>
        </div>
      {:else if variant === "pulse"}
        <div class={`bg-blue-600 rounded-full animate-pulse ${sizeClasses[size]} ${className}`}></div>
      {/if}
      
      {#if text}
        <p class={`text-gray-600 ${textSizeClasses[size]}`}>{text}</p>
      {/if}
    </div>
  </div>
{:else}
  <div class="flex items-center space-x-3">
    {#if variant === "spinner"}
      <div class={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`}></div>
    {:else if variant === "dots"}
      <div class="flex space-x-1">
        <div class={`bg-blue-600 rounded-full animate-bounce ${sizeClasses[size]}`} style="animation-delay: 0ms"></div>
        <div class={`bg-blue-600 rounded-full animate-bounce ${sizeClasses[size]}`} style="animation-delay: 150ms"></div>
        <div class={`bg-blue-600 rounded-full animate-bounce ${sizeClasses[size]}`} style="animation-delay: 300ms"></div>
      </div>
    {:else if variant === "pulse"}
      <div class={`bg-blue-600 rounded-full animate-pulse ${sizeClasses[size]} ${className}`}></div>
    {/if}
    
    {#if text}
      <span class={`text-gray-600 ${textSizeClasses[size]}`}>{text}</span>
    {/if}
  </div>
{/if}
