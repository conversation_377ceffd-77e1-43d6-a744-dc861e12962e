<script lang="ts">
  import type { HTMLAttributes } from "svelte/elements";

  interface Props extends HTMLAttributes<HTMLDivElement> {
    variant?: "default" | "elevated" | "outlined" | "glass";
    padding?: "none" | "sm" | "md" | "lg";
    hover?: boolean;
    children?: any;
  }

  let {
    variant = "default",
    padding = "md",
    hover = false,
    class: className = "",
    children,
    ...restProps
  }: Props = $props();

  // 基础样式
  const baseClasses = "rounded-xl transition-all duration-200";

  // 变体样式
  const variantClasses = {
    default: "bg-white border border-gray-200",
    elevated: "bg-white shadow-md",
    outlined: "bg-transparent border-2 border-gray-200",
    glass: "bg-white/60 backdrop-blur-sm border border-gray-200/50"
  };

  // 内边距样式
  const paddingClasses = {
    none: "",
    sm: "p-4",
    md: "p-6",
    lg: "p-8"
  };

  // 悬停效果
  const hoverClasses = $derived(hover ? "hover:shadow-lg hover:-translate-y-1 cursor-pointer" : "");

  // 组合样式
  const computedClasses = $derived([
    baseClasses,
    variantClasses[variant],
    paddingClasses[padding],
    hoverClasses,
    className
  ].filter(Boolean).join(" "));
</script>

<div
  class={computedClasses}
  {...restProps}
>
  {@render children?.()}
</div>
