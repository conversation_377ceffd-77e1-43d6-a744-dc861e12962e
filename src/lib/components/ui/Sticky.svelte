<script lang="ts">
    import { onDestroy, onMount } from "svelte";

    interface Props {
        class?: string;
        children: any;
        offset?: number;
    }

    let { class: className = "", children, offset = 0 }: Props = $props();

    let stickyElement: HTMLDivElement | undefined = $state();
    let isSticky = $state(false);

    function onscroll() {
        if (stickyElement) {
            const rect = stickyElement.getBoundingClientRect();
            const shouldBeSticky = rect.top <= offset;

            if (shouldBeSticky !== isSticky) {
                isSticky = shouldBeSticky;

                if (isSticky) {
                    stickyElement.classList.add("is-sticky");
                } else {
                    stickyElement.classList.remove("is-sticky");
                }
            }
        }
    }

    onMount(() => {
        window.addEventListener("scroll", onscroll, { passive: true });
        requestAnimationFrame(onscroll);

        return (() => {
            window.removeEventListener("scroll", onscroll);
        });
    });

</script>

<!-- 修复点击问题的关键样式 -->
<div
    class="sticky top-0 {className}"
    bind:this={stickyElement}
    style="z-index: 50; pointer-events: auto;"
>
    {@render children(isSticky)}
</div>

<style>
    /* 确保 sticky 元素和子元素可以接收点击事件 */
    :global(.is-sticky) {
        pointer-events: auto !important;
    }

    :global(.is-sticky *) {
        pointer-events: auto !important;
    }

    /* 确保按钮在 sticky 状态下可点击 */
    :global(.is-sticky button) {
        pointer-events: auto !important;
        position: relative;
        z-index: 1;
    }
</style>
