<script lang="ts">
  import { onMount } from "svelte";
  import Loading from "./Loading.svelte";

  interface Props {
    threshold?: number;
    rootMargin?: string;
    once?: boolean;
    placeholder?: any;
    children?: any;
  }

  let {
    threshold = 0.1,
    rootMargin = "50px",
    once = true,
    placeholder,
    children
  }: Props = $props();

  let element: HTMLElement;
  let isVisible = $state(false);
  let hasLoaded = $state(false);

  onMount(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            isVisible = true;
            hasLoaded = true;
            
            if (once) {
              observer.unobserve(element);
            }
          } else if (!once) {
            isVisible = false;
          }
        });
      },
      {
        threshold,
        rootMargin
      }
    );

    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  });
</script>

<div bind:this={element} class="lazy-load-container">
  {#if isVisible || hasLoaded}
    {@render children?.()}
  {:else if placeholder}
    {@render placeholder()}
  {:else}
    <div class="flex items-center justify-center p-8">
      <Loading size="md" text="加载中..." />
    </div>
  {/if}
</div>

<style>
  .lazy-load-container {
    min-height: 100px;
  }
</style>
