<script lang="ts">
  interface Props {
    value?: string;
    placeholder?: string;
    disabled?: boolean;
    readonly?: boolean;
    required?: boolean;
    label?: string;
    error?: string;
    hint?: string;
    rows?: number;
    cols?: number;
    resize?: "none" | "both" | "horizontal" | "vertical";
    fullWidth?: boolean;
    class?: string;
    id?: string;
    name?: string;
    maxlength?: number;
    oninput?: (event: Event) => void;
    onchange?: (event: Event) => void;
    onfocus?: (event: FocusEvent) => void;
    onblur?: (event: FocusEvent) => void;
  }

  let {
    value = $bindable(),
    placeholder,
    disabled = false,
    readonly = false,
    required = false,
    label,
    error,
    hint,
    rows = 4,
    cols,
    resize = "vertical",
    fullWidth = false,
    class: className = "",
    id,
    name,
    maxlength,
    oninput,
    onchange,
    onfocus,
    onblur
  }: Props = $props();

  // 基础样式
  const baseClasses = "border rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 text-base";

  // 调整大小样式
  const resizeClasses = {
    none: "resize-none",
    both: "resize",
    horizontal: "resize-x",
    vertical: "resize-y"
  };

  // 状态样式
  const stateClasses = $derived(error
    ? "border-red-300 focus:border-red-500 focus:ring-red-500"
    : "border-gray-300 hover:border-gray-400");

  // 组合样式
  const computedClasses = $derived([
    baseClasses,
    resizeClasses[resize],
    stateClasses,
    fullWidth ? "w-full" : "",
    className
  ].filter(Boolean).join(" "));

  // 生成唯一ID
  const textareaId = $derived(id || `textarea-${Math.random().toString(36).substring(2, 11)}`);
</script>

<div class={fullWidth ? "w-full" : ""}>
  {#if label}
    <label for={textareaId} class="block text-sm font-medium text-gray-700 mb-1">
      {label}
      {#if required}
        <span class="text-red-500">*</span>
      {/if}
    </label>
  {/if}
  
  <textarea
    bind:value
    {placeholder}
    {disabled}
    {readonly}
    {required}
    {rows}
    {cols}
    {name}
    {maxlength}
    id={textareaId}
    class={computedClasses}
    {oninput}
    {onchange}
    {onfocus}
    {onblur}
  ></textarea>
  
  {#if error}
    <p class="mt-1 text-sm text-red-600">{error}</p>
  {:else if hint}
    <p class="mt-1 text-sm text-gray-500">{hint}</p>
  {/if}
</div>
