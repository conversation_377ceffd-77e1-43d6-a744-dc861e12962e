<script lang="ts">
    import { autoFocus } from "$lib/actions/autoFocus";
    import { selectRange } from "$lib/actions/selectRange";
    import {
        keyboardShortcut,
        type KeyboardShortcut,
    } from "$lib/actions/window";
    // import { createEventDispatcher } from "svelte";
    // const dispatch = createEventDispatcher();

    export let id: string = "";
    export let suggestion: string = "";
    export let value: string = "";
    export let placeholder = "";
    export let disabled: boolean = false;
    export let readonly: boolean = false;
    export let autofocus: boolean = false;
    export let shortcuts: KeyboardShortcut[] = [];
    export let selectrange: { start: number; end: number } | null = null;

    export let style = "";
    export let flex = "";

    $: {
        if (textspan) {
            if (suggestion === "") {
                textspan.textContent = `${value} `;
                textspan.style.visibility = "hidden";
            } else {
                textspan.textContent = `${suggestion} `;
                textspan.style.color = "#777";
                textspan.style.visibility = "";
            }
            // boundingClientRect = textspan.getBoundingClientRect();

            // console.log("textspan", textspan.style.visibility || "show");
        }
    }

    let textspan: HTMLElement;
    let textarea: HTMLTextAreaElement;
</script>

<div class={flex}>
    <span bind:this={textspan} class={$$props.class} {style}></span>
    <textarea
        bind:this={textarea}
        class={$$props.class}
        {style}
        {id}
        bind:value
        on:input
        on:change
        on:focus
        on:blur
        on:selectstart
        on:selectionchange
        on:select
        {disabled}
        {readonly}
        {placeholder}
        use:autoFocus={autofocus}
        use:keyboardShortcut={shortcuts}
        use:selectRange={selectrange}
    ></textarea>
</div>

<style>
    div {
        position: relative;
        z-index: 5;
    }
    span {
        display: block;
        white-space: pre-wrap;
        word-wrap: break-word;
        text-align: left;
        width: 100%;
        height: 100%;
        resize: none;
        overflow: hidden;
    }
    textarea {
        text-align: left;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        resize: none;
        overflow: hidden;
        z-index: 4;
    }
</style>
