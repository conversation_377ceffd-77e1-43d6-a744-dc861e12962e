<script lang="ts">
  interface Props {
    icon?: any;
    title: string;
    description?: string;
    actions?: any;
    children?: any;
  }

  let {
    icon,
    title,
    description,
    actions,
    children
  }: Props = $props();
</script>

<div class="text-center py-16">
  {#if icon}
    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
      {@render icon()}
    </div>
  {/if}
  
  <h3 class="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
  
  {#if description}
    <p class="text-gray-600 mb-8 max-w-md mx-auto">
      {description}
    </p>
  {/if}
  
  {#if actions}
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
      {@render actions()}
    </div>
  {/if}
  
  {#if children}
    {@render children()}
  {/if}
</div>
