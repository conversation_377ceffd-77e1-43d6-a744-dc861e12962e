<!-- src/lib/components/Toast.svelte -->
<script lang="ts">
  import { fly } from "svelte/transition";
  import { CheckCircle, XCircle, Info, AlertTriangle, X } from "@lucide/svelte";
  import toasts, { removeToast } from "./toastStore";

  const typeConfig = {
    success: {
      classes: "bg-green-50 border-green-200 text-green-800",
      icon: CheckCircle,
      iconClasses: "text-green-400"
    },
    error: {
      classes: "bg-red-50 border-red-200 text-red-800",
      icon: XCircle,
      iconClasses: "text-red-400"
    },
    info: {
      classes: "bg-blue-50 border-blue-200 text-blue-800",
      icon: Info,
      iconClasses: "text-blue-400"
    },
    warning: {
      classes: "bg-yellow-50 border-yellow-200 text-yellow-800",
      icon: AlertTriangle,
      iconClasses: "text-yellow-400"
    },
  };
</script>

<div class="fixed top-4 right-4 z-50 max-w-sm">
  <div class="flex flex-col space-y-3">
    {#each $toasts as toast (toast.id)}
      {@const config = typeConfig[toast.type]}
      <div
        in:fly={{ x: 300, duration: 300 }}
        out:fly={{ x: 300, duration: 300 }}
        class={`rounded-lg border shadow-lg p-4 ${config.classes} relative`}
      >
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svelte:component this={config.icon} class={`w-5 h-5 ${config.iconClasses}`} />
          </div>
          <div class="ml-3 flex-1">
            <p class="text-sm font-medium whitespace-pre-line">
              {toast.message}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0">
            <button
              type="button"
              class="inline-flex rounded-md p-1.5 hover:bg-black/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
              on:click={() => removeToast(toast.id)}
            >
              <X class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    {/each}
  </div>
</div>
