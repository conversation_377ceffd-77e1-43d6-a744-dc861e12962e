// src/lib/stores/toastStore.ts
import { writable } from 'svelte/store';

export type ToastType = 'success' | 'error' | 'info' | 'warning';

export interface Toast {
  id: number;
  message: string;
  type: ToastType;
  duration?: number; // 默认 3000ms
}

const toasts = writable<Toast[]>([]);
let counter = 0;

export function addToast(toast: Omit<Toast, 'id'>) {
  const id = counter++;
  toasts.update((all) => [...all, { ...toast, id }]);

  setTimeout(() => {
    removeToast(id);
  }, toast.duration ?? 3000);
}

export function removeToast(id: number) {
  toasts.update((all) => all.filter((t) => t.id !== id));
}

export default toasts;
