<!-- src/lib/components/Sidebar.svelte -->
<script lang="ts">
  import { onMount } from "svelte";
  import { browser } from "$app/environment";
  import { derived } from "svelte/store";
  import { isSidebarOpen } from "./sidebar";

  let isDesktop = true;

  if (browser) {
    const checkViewport = () => {
      isDesktop = window.innerWidth >= 1024;
    };

    onMount(() => {
      checkViewport();
      window.addEventListener("resize", checkViewport);
      return () => window.removeEventListener("resize", checkViewport);
    });
  }

  const displaySidebar = derived(isSidebarOpen, ($isSidebarOpen) =>
    isDesktop ? true : $isSidebarOpen,
  );

  function closeSidebar() {
    if (!isDesktop) {
      isSidebarOpen.set(false);
    }
  }
</script>

<!-- Overlay for mobile -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore element_invalid_self_closing_tag -->
<div
  class="fixed inset-0 bg-black bg-opacity-50 z-30 transition-opacity duration-300 lg:hidden"
  class:hidden={!$isSidebarOpen}
  on:click={closeSidebar}
/>

<!-- Sidebar -->
<aside
  class={`fixed top-0 left-0 h-full w-64 bg-white shadow-lg z-40 transform transition-transform duration-300
    ${$displaySidebar ? "translate-x-0" : "-translate-x-full"}
    lg:translate-x-0 lg:static lg:shadow-none`}
>
  <div class="p-4 font-bold text-lg border-b">📚 Sidebar</div>
  <ul class="p-4 space-y-2">
    <slot />
  </ul>
</aside>
