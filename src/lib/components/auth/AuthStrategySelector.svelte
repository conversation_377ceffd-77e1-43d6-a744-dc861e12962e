<script lang="ts">
  import { authService } from "$lib/services/auth.service";
  import { addToast } from "$lib/components/toast/toastStore";
  import Button from "$lib/components/ui/Button.svelte";
  import { Wifi, WifiOff, Settings, Zap, Shield } from "@lucide/svelte";
    import { AuthStrategy } from "$lib/auth/auth-strategy.types";

  interface Props {
    showAdvanced?: boolean;
    compact?: boolean;
  }

  let { showAdvanced = false, compact = false }: Props = $props();

  let currentStrategy = $state<AuthStrategy>(AuthStrategy.SERVER_PROXY);
  let isLoading = $state(false);
  let strategiesHealth = $state<Record<string, any>>({});
  let autoSwitch = $state(true);

  // 初始化
  function initialize() {
    try {
      currentStrategy = authService.getCurrentStrategyType();
      const config = authService.getConfig();
      autoSwitch = config.autoSwitch;
      checkHealth();
    } catch (error) {
      console.error('Failed to initialize auth strategy selector:', error);
    }
  }

  // 检查策略健康状态
  async function checkHealth() {
    try {
      strategiesHealth = await authService.getStrategiesHealth();
    } catch (error) {
      console.error('Failed to check strategies health:', error);
    }
  }

  // 切换策略
  async function switchStrategy(strategy: AuthStrategy) {
    if (strategy === currentStrategy) return;

    isLoading = true;
    try {
      await authService.switchStrategy(strategy);
      currentStrategy = strategy;
      
      if (!compact) {
        addToast({
          type: "success",
          message: `已切换到${getStrategyDisplayName(strategy)}`,
        });
      }
    } catch (error) {
      addToast({
        type: "error",
        message: "策略切换失败",
      });
    } finally {
      isLoading = false;
    }
  }

  // 切换自动选择
  async function toggleAutoSwitch() {
    try {
      authService.updateConfig({ autoSwitch: !autoSwitch });
      autoSwitch = !autoSwitch;
      
      if (!compact) {
        addToast({
          type: "success",
          message: `自动策略选择${autoSwitch ? '已启用' : '已禁用'}`,
        });
      }
    } catch (error) {
      addToast({
        type: "error",
        message: "配置更新失败",
      });
    }
  }

  // 刷新网络状态
  async function refreshNetwork() {
    isLoading = true;
    try {
      const recommendedStrategy = await authService.refreshAndRecommend();
      currentStrategy = authService.getCurrentStrategyType();
      await checkHealth();
      
      if (!compact) {
        addToast({
          type: "info",
          message: `推荐策略: ${getStrategyDisplayName(recommendedStrategy)}`,
        });
      }
    } catch (error) {
      addToast({
        type: "error",
        message: "网络状态刷新失败",
      });
    } finally {
      isLoading = false;
    }
  }

  function getStrategyDisplayName(strategy: AuthStrategy): string {
    const names = {
      [AuthStrategy.FIREBASE_SDK]: "Firebase直连",
      [AuthStrategy.SERVER_PROXY]: "服务端代理",
      [AuthStrategy.HYBRID]: "混合模式"
    };
    return names[strategy] || strategy;
  }

  function getStrategyIcon(strategy: AuthStrategy) {
    switch (strategy) {
      case AuthStrategy.FIREBASE_SDK:
        return Wifi;
      case AuthStrategy.SERVER_PROXY:
        return Shield;
      case AuthStrategy.HYBRID:
        return Zap;
      default:
        return Settings;
    }
  }

  function getStrategyDescription(strategy: AuthStrategy): string {
    const descriptions = {
      [AuthStrategy.FIREBASE_SDK]: "直接连接Firebase服务，速度更快",
      [AuthStrategy.SERVER_PROXY]: "通过服务端代理，适用于网络受限环境",
      [AuthStrategy.HYBRID]: "智能混合模式，自动选择最佳方式"
    };
    return descriptions[strategy] || "";
  }

  function isStrategyHealthy(strategy: AuthStrategy): boolean {
    const health = strategiesHealth[strategy];
    return health?.healthy ?? false;
  }

  // 初始化
  initialize();
</script>

{#if compact}
  <!-- 紧凑模式 -->
  <div class="flex items-center space-x-2">
    <div class="flex items-center space-x-1">
      {#each [AuthStrategy.SERVER_PROXY, AuthStrategy.FIREBASE_SDK] as strategy}
        <button
          onclick={() => switchStrategy(strategy)}
          disabled={isLoading}
          class="p-2 rounded-md transition-colors {currentStrategy === strategy 
            ? 'bg-blue-100 text-blue-600' 
            : 'text-gray-400 hover:text-gray-600'} {isLoading ? 'opacity-50' : ''}"
          title={getStrategyDisplayName(strategy)}
        >
          <svelte:component this={getStrategyIcon(strategy)} size={16} />
          {#if isStrategyHealthy(strategy)}
            <div class="w-2 h-2 bg-green-400 rounded-full absolute -top-1 -right-1"></div>
          {/if}
        </button>
      {/each}
    </div>
    
    <button
      onclick={toggleAutoSwitch}
      disabled={isLoading}
      class="p-2 rounded-md transition-colors {autoSwitch 
        ? 'bg-green-100 text-green-600' 
        : 'text-gray-400 hover:text-gray-600'} {isLoading ? 'opacity-50' : ''}"
      title={`自动策略选择: ${autoSwitch ? '已启用' : '已禁用'}`}
    >
      <Settings size={16} />
    </button>
  </div>
{:else}
  <!-- 完整模式 -->
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-medium">认证策略</h3>
      <Button
        onclick={refreshNetwork}
        loading={isLoading}
        variant="outline"
        size="sm"
      >
        <WifiOff size={16} class="mr-2" />
        刷新网络
      </Button>
    </div>

    <!-- 策略选择 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
      {#each [AuthStrategy.SERVER_PROXY, AuthStrategy.FIREBASE_SDK] as strategy}
        <button
          onclick={() => switchStrategy(strategy)}
          disabled={isLoading}
          class="p-4 border rounded-lg text-left transition-all {currentStrategy === strategy 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-200 hover:border-gray-300'} {isLoading ? 'opacity-50' : ''}"
        >
          <div class="flex items-start space-x-3">
            <div class="relative">
              <svelte:component this={getStrategyIcon(strategy)} size={20} class="text-gray-600" />
              {#if isStrategyHealthy(strategy)}
                <div class="w-2 h-2 bg-green-400 rounded-full absolute -top-1 -right-1"></div>
              {/if}
            </div>
            <div class="flex-1">
              <div class="font-medium text-sm">{getStrategyDisplayName(strategy)}</div>
              <div class="text-xs text-gray-500 mt-1">{getStrategyDescription(strategy)}</div>
              {#if strategiesHealth[strategy]}
                <div class="text-xs mt-1 {isStrategyHealthy(strategy) ? 'text-green-600' : 'text-red-600'}">
                  {isStrategyHealthy(strategy) ? '健康' : '不可用'}
                  {#if strategiesHealth[strategy].latency}
                    ({strategiesHealth[strategy].latency}ms)
                  {/if}
                </div>
              {/if}
            </div>
          </div>
        </button>
      {/each}
    </div>

    <!-- 自动切换选项 -->
    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div>
        <div class="font-medium text-sm">自动策略选择</div>
        <div class="text-xs text-gray-500">根据网络环境自动选择最佳认证策略</div>
      </div>
      <button
        onclick={toggleAutoSwitch}
        disabled={isLoading}
        class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors {autoSwitch 
          ? 'bg-blue-600' 
          : 'bg-gray-200'} {isLoading ? 'opacity-50' : ''}"
      >
        <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform {autoSwitch 
          ? 'translate-x-6' 
          : 'translate-x-1'}"></span>
      </button>
    </div>

    {#if showAdvanced}
      <!-- 高级选项 -->
      <details class="text-sm">
        <summary class="cursor-pointer text-gray-600 hover:text-gray-800">高级选项</summary>
        <div class="mt-2 p-3 bg-gray-50 rounded text-xs">
          <pre>{JSON.stringify(authService.getConfig(), null, 2)}</pre>
        </div>
      </details>
    {/if}
  </div>
{/if}
