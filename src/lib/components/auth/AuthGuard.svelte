<script lang="ts">
  import { goto } from "$app/navigation";
  import { onMount } from "svelte";
  import { authService } from "$lib/services/auth.service";
  import { userAuth } from "$lib/stores/app.store";
  import Loading from "$lib/components/ui/Loading.svelte";

  interface Props {
    ignoreAllCheck?: boolean;
    requireAuth?: boolean;
    requireEmailVerified?: boolean;
    requireActivated?: boolean;
    redirectTo?: string;
    children?: any;
  }

  let {
    ignoreAllCheck = false,
    requireAuth = true,
    requireEmailVerified = true,
    requireActivated = true,
    redirectTo = "/",
    children,
  }: Props = $props();

  let isChecking = $state(true);
  let isAuthorized = $state(false);

  onMount(() => {
    if (ignoreAllCheck) {
      isAuthorized = true;
      isChecking = false;
      return;
    }

    checkAuthStatus();

    // 监听用户状态变化
    const unsubscribe = userAuth.subscribe(() => {
      checkAuthStatus();
    });

    return unsubscribe;
  });

  function checkAuthStatus() {
    isChecking = true;

    try {
      // 检查基本认证状态
      if (!authService.isAuthenticated()) {
        if (requireAuth) {
          console.warn(
            "AuthGuard: User not authenticated, redirecting to login",
          );
          console.log("AuthGuard: User not authenticated, redirecting to login");
          
          goto("/login");
        } else {
          isAuthorized = true;
        }
        return;
      }

      // 检查邮箱验证状态
      if (!authService.isEmailVerified()) {
        if (requireEmailVerified) {
          console.warn(
            "AuthGuard: Email not verified, redirecting to verify-email",
          );
          goto("/auth/verify-email");
        } else {
          isAuthorized = true;
        }
        return;
      }

      // 检查激活状态
      if (!authService.isActivated()) {
        if (requireActivated) {
          console.warn(
            "AuthGuard: Account not activated, redirecting to activate",
          );
          goto("/auth/activate");
        } else {
          isAuthorized = true;
        }
        return;
      }

      // 所有检查通过
      goto(redirectTo);
    } catch (error) {
      console.error("AuthGuard: Error checking auth status", error);
      goto("/login");
    } finally {
      isChecking = false;
      isAuthorized = true;
    }
  }
</script>

{#if isChecking}
  <!-- 检查认证状态时显示加载动画 -->
  <div class="min-h-screen flex items-center justify-center">
    <Loading text="验证身份中..." size="lg" fullScreen />
  </div>
{:else if isAuthorized}
  <!-- 认证通过，显示子组件 -->
  {@render children?.()}
{:else}
  <!-- 认证失败，显示空白页面（因为会重定向） -->
  <div class="min-h-screen flex items-center justify-center">
    <Loading text="跳转中..." size="lg" fullScreen />
  </div>
{/if}
