<script lang="ts">
  import { Check, X } from "@lucide/svelte";

  interface Props {
    password: string;
    showDetails?: boolean;
  }

  let { password, showDetails = true }: Props = $props();

  // 密码强度检查规则
  const checks = $derived([
    {
      label: "至少8个字符",
      valid: password.length >= 8,
      required: true
    },
    {
      label: "包含大写字母",
      valid: /[A-Z]/.test(password),
      required: false
    },
    {
      label: "包含小写字母",
      valid: /[a-z]/.test(password),
      required: true
    },
    {
      label: "包含数字",
      valid: /\d/.test(password),
      required: false
    },
    {
      label: "包含特殊字符",
      valid: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      required: false
    }
  ]);

  // 计算密码强度
  const strength = $derived(() => {
    if (password.length === 0) return { level: 0, label: "", color: "" };

    const requiredPassed = checks.filter(c => c.required && c.valid).length;
    const requiredTotal = checks.filter(c => c.required).length;
    const optionalPassed = checks.filter(c => !c.required && c.valid).length;

    // 必须满足所有必需条件
    if (requiredPassed < requiredTotal) {
      return { level: 1, label: "弱", color: "text-red-600" };
    }

    // 根据可选条件数量确定强度
    if (optionalPassed === 0) {
      return { level: 2, label: "中等", color: "text-yellow-600" };
    } else if (optionalPassed <= 2) {
      return { level: 3, label: "强", color: "text-green-600" };
    } else {
      return { level: 4, label: "很强", color: "text-green-700" };
    }
  });

  // 进度条宽度
  const progressWidth = $derived(() => {
    return `${(strength().level / 4) * 100}%`;
  });

  // 进度条颜色
  const progressColor = $derived(() => {
    switch (strength().level) {
      case 1: return "bg-red-500";
      case 2: return "bg-yellow-500";
      case 3: return "bg-green-500";
      case 4: return "bg-green-600";
      default: return "bg-gray-300";
    }
  });
</script>

{#if password.length > 0}
  <div class="mt-2 space-y-2">
    <!-- 强度指示器 -->
    <div class="flex items-center justify-between text-sm">
      <span class="text-gray-600">密码强度:</span>
      <span class={strength().color + " font-medium"}>{strength().label}</span>
    </div>

    <!-- 进度条 -->
    <div class="w-full bg-gray-200 rounded-full h-2">
      <div
        class={`h-2 rounded-full transition-all duration-300 ${progressColor}`}
        style="width: {progressWidth}"
      ></div>
    </div>

    <!-- 详细检查项 -->
    {#if showDetails}
      <div class="space-y-1">
        {#each checks as check}
          <div class="flex items-center text-xs">
            {#if check.valid}
              <Check class="w-3 h-3 text-green-500 mr-2" />
            {:else}
              <X class="w-3 h-3 text-gray-400 mr-2" />
            {/if}
            <span class={check.valid ? "text-green-600" : "text-gray-500"}>
              {check.label}
              {#if check.required}
                <span class="text-red-500">*</span>
              {/if}
            </span>
          </div>
        {/each}
      </div>
    {/if}
  </div>
{/if}