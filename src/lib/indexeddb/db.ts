
/**
 * @file IndexedDB 数据库通用封装
 * @description 提供一个通用的 IndexedDB 包装类，简化数据库的创建、连接、增删改查等操作。
 */

import { browser } from "$app/environment";

/**
 * 数据库记录的通用接口。
 * @interface DataRecord
 * @property {string} [id] - 记录的唯一标识符，通常是主键。
 * @property {string} [timestamp] - 记录的时间戳，ISO 格式字符串。
 * @property {any} [key] - 允许任何其他类型的属性。
 */
export interface DataRecord {
    id?: string,
    timestamp?: string,
    [key: string]: any;
}

/**
 * APP 通用 IndexedDB 数据库操作类。
 * @class Database
 */
export class Database {
    private dbName: string;
    private dbVersion: number;
    private stores: string[];
    private db: IDBDatabase | null = null;

    /**
     * 创建一个 Database 实例。
     * @param {string} dbName - 数据库名称。
     * @param {number} dbVersion - 数据库版本。
     * @param {string[]} stores - 需要创建或操作的对象存储（表）名称列表。
     * @throws {Error} 如果没有提供任何存储名称。
     */
    constructor(dbName: string, dbVersion: number, stores: string[]) {
        if (stores.length == 0) throw new Error('No store name provided');

        this.dbName = dbName;
        this.dbVersion = dbVersion;
        this.stores = stores;
    }

    /**
     * 打开数据库连接。如果数据库或对象存储不存在，则会进行创建或升级。
     * @returns {Promise<IDBDatabase>} - 返回一个 Promise，成功时解析为 IDBDatabase 实例。
     */
    async open(): Promise<IDBDatabase> {
        if (this.db) return this.db;
        return new Promise((resolve, reject) => {
            if (this.db) {
                return resolve(this.db);
            }

            if (!browser) {
                return reject("indexedDB not in browser");
            }
            const request = indexedDB.open(this.dbName, this.dbVersion);

            // 当数据库需要升级时调用（包括首次创建）
            request.onupgradeneeded = (event) => {
                const db = (event.target as IDBOpenDBRequest).result;
                this.stores.forEach(storeName => {
                    if (!db.objectStoreNames.contains(storeName)) {
                        const store = db.createObjectStore(storeName, {
                            keyPath: "id", // 主键字段
                            autoIncrement: true, // 自动递增主键
                        });

                        // 创建一个时间戳索引，用于排序
                        store.createIndex("timestamp", "timestamp", {
                            unique: false,
                        });
                    }
                });
            };

            // 成功打开数据库
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };

            // 打开数据库失败
            request.onerror = () => {
                console.error("创建对象存储失败: ", request.error);
                reject(request.error);
            };
        });
    }

    /**
     * 获取一个对象存储（表）的实例以进行操作。
     * @private
     * @param {string} storeName - 对象存储的名称。
     * @param {IDBTransactionMode} [mode='readonly'] - 事务模式（'readonly' 或 'readwrite'）。
     * @returns {IDBObjectStore} - 返回指定名称的对象存储实例。
     * @throws {Error} 如果数据库尚未打开。
     */
    private async getStore(storeName: string, mode: IDBTransactionMode = 'readonly'): Promise<IDBObjectStore> {
        const db = await this.open();
        const tx = db.transaction(storeName, mode);
        return tx.objectStore(storeName);
    }

    /**
     * 向指定的对象存储中添加一条新记录。
     * @param {string} storeName - 对象存储的名称。
     * @param {DataRecord} data - 要添加的数据记录。
     * @returns {Promise<IDBValidKey>} - 返回一个 Promise，成功时解析为新记录的主键。
     */
    async add(storeName: string, data: DataRecord): Promise<IDBValidKey> {
        const store = await this.getStore(storeName, 'readwrite');

        return new Promise((resolve, reject) => {
            // 如果没有提供 timestamp，则自动添加当前时间
            if (!data.timestamp) {
                data.timestamp = new Date().toISOString();
            }
            try {
                const request = store.add(data);
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject({ error: request.error, data });
            } catch (err: any) {
                reject({ error: err, data });
            }
        });
    }

    /**
     * 更新指定的对象存储中的一条记录。
     * @param {string} storeName - 对象存储的名称。
     * @param {DataRecord} data - 要更新的数据记录。
     * @returns {Promise<void>} - 返回一个 Promise，操作完成时解析。
     */
    async update(storeName: string, data: DataRecord): Promise<void> {
        const store = await this.getStore(storeName, 'readwrite');

        return new Promise((resolve, reject) => {
            // 更新记录时，自动更新时间戳
            data.timestamp = new Date().toISOString();
            const request = store.put(data);
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 从指定的对象存储中删除一条记录。
     * @param {string} storeName - 对象存储的名称。
     * @param {IDBValidKey} key - 要删除记录的主键。
     * @returns {Promise<void>} - 返回一个 Promise，操作完成时解析。
     */
    async delete(storeName: string, key: IDBValidKey): Promise<void> {
        const store = await this.getStore(storeName, 'readwrite');

        return new Promise((resolve, reject) => {
            const request = store.delete(key);
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 从指定的对象存储中获取一条记录。
     * @param {string} storeName - 对象存储的名称。
     * @param {IDBValidKey} key - 要获取记录的主键。
     * @returns {Promise<DataRecord | undefined>} - 返回一个 Promise，成功时解析为找到的记录，否则为 undefined。
     */
    async get(storeName: string, key: IDBValidKey): Promise<DataRecord | undefined> {
        const store = await this.getStore(storeName, 'readwrite');

        return new Promise((resolve, reject) => {
            const request = store.get(key);
            request.onsuccess = () => {
                resolve(request.result)
            };
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 获取指定对象存储中的所有记录。
     * @param {string} storeName - 对象存储的名称。
     * @returns {Promise<DataRecord[]>} - 返回一个 Promise，成功时解析为所有记录的数组。
     */
    async all(storeName: string): Promise<DataRecord[]> {
        const store = await this.getStore(storeName, 'readonly');

        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 获取指定对象存储中的记录总数。
     * @param {string} storeName - 对象存储的名称。
     * @returns {Promise<number>} - 返回一个 Promise，成功时解析为记录总数。
     */
    async count(storeName: string): Promise<number> {
        const store = await this.getStore(storeName, 'readonly');

        return new Promise((resolve, reject) => {
            const request = store.count();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }


    /**
     * 从指定的对象存储中分页获取数据。
     * @param {string} storeName - 对象存储的名称。
     * @param {number} [page=1] - 页码，从 1 开始。
     * @param {number} [pageSize=10] - 每页的记录数。
     * @returns {Promise<DataRecord[]>} - 返回一个 Promise，成功时解析为当前页的记录数组。
     */
    async page(storeName: string, page: number = 1, pageSize: number = 10): Promise<DataRecord[]> {
        const store = await this.getStore(storeName, 'readonly');

        return new Promise((resolve, reject) => {
            const request = store.openCursor(null, 'prev'); // 从最新开始
            const results: DataRecord[] = [];
            let skipped = (page - 1) * pageSize;
            let collected = 0;

            request.onsuccess = () => {
                const cursor = request.result;
                if (cursor) {
                    if (skipped > 0) {
                        cursor.advance(skipped);
                        skipped = 0;
                    } else if (collected < pageSize) {
                        results.push(cursor.value);
                        collected++;
                        cursor.continue();
                    } else {
                        resolve(results);
                    }
                } else {
                    resolve(results);
                }
            };

            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 在指定的对象存储中搜索和排序数据。
     * @param {string} storeName - 对象存储的名称。
     * @param {string} query - 搜索查询字符串。
     * @param {{name: string, dir: "asc" | "desc"}} sort - 排序配置。
     * @returns {Promise<DataRecord[]>} - 返回一个 Promise，成功时解析为匹配和排序后的记录数组。
     */
    async search(storeName: string, query: string, sort?: { name: string, dir: "asc" | "desc" }): Promise<DataRecord[]> {
        const store = await this.getStore(storeName, 'readonly');

        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => {
                let results = request.result;

                // 如果有查询条件，则进行过滤
                if (query.trim()) {
                    results = results.filter((item) =>
                        JSON.stringify(item)
                            .toLowerCase()
                            .includes(query.toLowerCase()),
                    );
                }

                // 如果有排序条件，则进行排序
                if (sort && sort.name) {
                    results.sort((a, b) => {
                        const aVal = a[sort.name];
                        const bVal = b[sort.name];
                        const comparison =
                            aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
                        return sort.dir === "asc"
                            ? comparison
                            : -comparison;
                    });
                }

                resolve(results);
            };
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 清空指定的对象存储中的所有记录。
     * @param {string} storeName - 对象存储的名称。
     * @returns {Promise<void>} - 返回一个 Promise，操作完成时解析。
     */
    async clear(storeName: string): Promise<void> {
        const store = await this.getStore(storeName, 'readwrite');

        return new Promise((resolve, reject) => {
            const request = store.clear();
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 关闭数据库连接。
     */
    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
        }
    }
}

export const appModelsTestDB = new Database("app-models-test", 1, ["chats"]);
