/**
 * 激活码管理 API
 * 提供激活码的 CRUD 操作
 * 
 * 支持的操作：
 * - GET: 获取激活码列表（支持分页、搜索、筛选）
 * - POST: 创建新的激活码
 * - PUT: 批量更新激活码
 * - DELETE: 批量删除激活码
 */

import type { RequestHandler } from '@sveltejs/kit';
import { json, error } from '@sveltejs/kit';
import { initializeFirebaseAdmin } from '$lib/firebase/firebase-admin';
import admin from 'firebase-admin';
import { env } from '$env/dynamic/private';
import type {
  ActivationCode,
  CreateActivationCodeForm,
  ActivationCodeType,
  ActivationCodeStatus
} from '$lib/types/activation-code.types';
import { 
  generateActivationCode,
  validateActivationCodeFormat,
  calculateActivationCodeStatus,
  calculateRemainingDays
} from '$lib/utils/activation-code.utils';

// 初始化 Firebase Admin
initializeFirebaseAdmin();

/**
 * 验证管理员权限
 */
async function validateAdminAccess(request: Request): Promise<string> {
  const userToken = request.headers.get("Authorization")?.replace("Bearer ", "").trim();
  
  if (!userToken) {
    throw new Error("未提供认证令牌");
  }

  try {
    // 验证 Firebase ID Token
    const decodedToken = await admin.auth().verifyIdToken(userToken);
    const userEmail = decodedToken.email;

    if (!userEmail) {
      throw new Error("无法获取用户邮箱");
    }

    // 检查是否为管理员
    const adminEmail = env.ADMIN_EMAIL;
    if (!adminEmail || userEmail !== adminEmail) {
      throw new Error("您没有管理员权限");
    }

    return userEmail;
  } catch (err: any) {
    console.error("Admin validation failed:", err);
    throw new Error("权限验证失败");
  }
}

/**
 * 获取激活码列表
 * 支持分页、搜索、筛选和排序
 */
export const GET: RequestHandler = async ({ request, url }) => {
  try {
    // 验证管理员权限
    await validateAdminAccess(request);

    // 解析查询参数
    const searchParams = url.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100); // 最大100条
    const search = searchParams.get('search') || '';
    const type = searchParams.get('type') as ActivationCodeType | '';
    const status = searchParams.get('status') as ActivationCodeStatus | '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const db = admin.firestore();
    const collectionRef = db.collection('activation_codes'); // 使用新的集合名

    // 构建查询
    let query = collectionRef as admin.firestore.Query;

    // 类型筛选
    if (type) {
      query = query.where('type', '==', type);
    }

    // 排序
    const orderDirection = sortOrder === 'asc' ? 'asc' : 'desc';
    query = query.orderBy(sortBy, orderDirection);

    // 分页
    const offset = (page - 1) * limit;
    if (offset > 0) {
      query = query.offset(offset);
    }
    query = query.limit(limit);

    // 执行查询
    const snapshot = await query.get();
    
    // 处理结果
    let activationCodes: ActivationCode[] = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        code: doc.id,
        amount: data.amount || { agent: 0, completion: 0 },
        type: data.type || 'bonus',
        isActive: data.isActive || false,
        validity: data.validity || 12,
        createdAt: data.createdAt || Date.now(),
        createdEmail: data.createdEmail || env.ADMIN_EMAIL,
        expireAt: data.expireAt || 1,
        activatedAt: data.activatedAt || null,
        activatedEmail: data.activatedEmail || null,
        remark: data.remark || null,
        notForSale: data.notForSale || false
      };
    });

    // 计算状态和剩余天数
    activationCodes = activationCodes.map(code => ({
      ...code,
      status: calculateActivationCodeStatus(code),
      remainingDays: calculateRemainingDays(code)
    }));

    // 客户端搜索和状态筛选（因为 Firestore 查询限制）
    if (search) {
      const searchLower = search.toLowerCase();
      activationCodes = activationCodes.filter(code => 
        code.code.toLowerCase().includes(searchLower) ||
        (code.remark && code.remark.toLowerCase().includes(searchLower))
      );
    }

    if (status) {
      activationCodes = activationCodes.filter(code => code.status === status);
    }

    // 计算总数（简化版本，实际应该用单独的计数查询）
    const totalQuery = collectionRef;
    const totalSnapshot = await totalQuery.get();
    const total = totalSnapshot.size;
    const totalPages = Math.ceil(total / limit);

    return json({
      success: true,
      data: {
        items: activationCodes,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });

  } catch (err: any) {
    console.error("Error fetching activation codes:", err);
    return error(500, {
      message: err.message || "获取激活码列表失败"
    });
  }
};

/**
 * 创建新的激活码
 */
export const POST: RequestHandler = async ({ request }) => {
  try {
    // 验证管理员权限
    const adminEmail = await validateAdminAccess(request);

    // 解析请求体
    const formData: CreateActivationCodeForm = await request.json();

    // 验证必填字段
    if (!formData.amount || !formData.type) {
      return error(400, {
        message: "缺少必填字段：amount 和 type"
      });
    }

    // 验证额度
    if (formData.amount.agent < 0 || formData.amount.completion < 0) {
      return error(400, {
        message: "额度不能为负数"
      });
    }

    // 生成或验证激活码
    let activationCode = formData.code;
    if (!activationCode) {
      // 自动生成激活码
      activationCode = generateActivationCode();
    } else {
      // 验证手动输入的激活码格式
      if (!validateActivationCodeFormat(activationCode)) {
        return error(400, {
          message: "激活码格式不正确"
        });
      }
    }

    const db = admin.firestore();
    const docRef = db.collection('activation_codes').doc(activationCode);

    // 检查激活码是否已存在
    const existingDoc = await docRef.get();
    if (existingDoc.exists) {
      return error(409, {
        message: "激活码已存在"
      });
    }

    // 创建激活码数据
    const now = Date.now();
    const activationCodeData: Omit<ActivationCode, 'status' | 'remainingDays'> = {
      code: activationCode,
      amount: formData.amount,
      type: formData.type,
      isActive: false, // 新创建的激活码默认未激活
      validity: formData.validity,
      createdAt: now,
      createdEmail: adminEmail,
      expireAt: new Date(formData.expireAt).getTime(),
      activatedAt: null,
      activatedEmail: null,
      remark: formData.remark || null,
      notForSale: formData.notForSale || false
    };

    // 保存到数据库
    await docRef.set(activationCodeData);

    return json({
      success: true,
      message: "激活码创建成功",
      data: {
        ...activationCodeData,
        status: calculateActivationCodeStatus(activationCodeData),
        remainingDays: calculateRemainingDays(activationCodeData)
      }
    });

  } catch (err: any) {
    console.error("Error creating activation code:", err);
    return error(500, {
      message: err.message || "创建激活码失败"
    });
  }
};

/**
 * 批量更新激活码
 * TODO: 实现批量更新功能
 */
export const PUT: RequestHandler = async ({ request }) => {
  try {
    // 验证管理员权限
    await validateAdminAccess(request);

    // TODO: 实现批量更新逻辑
    return json({
      success: false,
      message: "批量更新功能暂未实现"
    });

  } catch (err: any) {
    console.error("Error updating activation codes:", err);
    return error(500, {
      message: err.message || "更新激活码失败"
    });
  }
};

/**
 * 批量删除激活码
 * TODO: 实现批量删除功能
 */
export const DELETE: RequestHandler = async ({ request }) => {
  try {
    // 验证管理员权限
    await validateAdminAccess(request);

    // TODO: 实现批量删除逻辑
    return json({
      success: false,
      message: "批量删除功能暂未实现"
    });

  } catch (err: any) {
    console.error("Error deleting activation codes:", err);
    return error(500, {
      message: err.message || "删除激活码失败"
    });
  }
};
