/**
 * 单个激活码操作 API
 * 提供单个激活码的详细操作
 * 
 * 支持的操作：
 * - GET: 获取单个激活码详情
 * - PATCH: 更新单个激活码
 * - DELETE: 删除单个激活码
 */

import type { RequestHandler } from '@sveltejs/kit';
import { json, error } from '@sveltejs/kit';
import { initializeFirebaseAdmin } from '$lib/firebase/firebase-admin';
import admin from 'firebase-admin';
import { env } from '$env/dynamic/private';
import type {
  ActivationCode,
  UpdateActivationCodeForm
} from '$lib/types/activation-code.types';
import {
  calculateActivationCodeStatus,
  calculateRemainingDays
} from '$lib/utils/activation-code.utils';

// 初始化 Firebase Admin
initializeFirebaseAdmin();

/**
 * 验证管理员权限
 */
async function validateAdminAccess(request: Request): Promise<string> {
  const userToken = request.headers.get("Authorization")?.replace("Bearer ", "").trim();

  if (!userToken) {
    throw new Error("未提供认证令牌");
  }

  try {
    // 验证 Firebase ID Token
    const decodedToken = await admin.auth().verifyIdToken(userToken);
    const userEmail = decodedToken.email;

    if (!userEmail) {
      throw new Error("无法获取用户邮箱");
    }

    // 检查是否为管理员
    const adminEmail = env.ADMIN_EMAIL;
    if (!adminEmail || userEmail !== adminEmail) {
      throw new Error("您没有管理员权限");
    }

    return userEmail;
  } catch (err: any) {
    console.error("Admin validation failed:", err);
    throw new Error("权限验证失败");
  }
}

/**
 * 获取单个激活码详情
 */
export const GET: RequestHandler = async ({ request, params }) => {
  try {
    // 验证管理员权限
    await validateAdminAccess(request);

    const { id } = params;
    if (!id) {
      return error(400, {
        message: "缺少激活码ID"
      });
    }

    const db = admin.firestore();
    const docRef = db.collection('activation_codes').doc(id);
    const doc = await docRef.get();

    if (!doc.exists) {
      return error(404, {
        message: "激活码不存在"
      });
    }

    const data = doc.data()!;
    const activationCode: ActivationCode = {
      code: doc.id,
      amount: data.amount || { agent: 0, completion: 0 },
      type: data.type || 'bonus',
      isActive: data.isActive || false,
      validity: data.validity || 0,
      createdAt: data.createdAt || Date.now(),
      expireAt: data.expireAt || 0,
      activatedAt: data.activatedAt || null,
      activatedEmail: data.activatedEmail || null,
      remark: data.remark || null,
      notForSale: data.notForSale || false
    };

    activationCode.status = calculateActivationCodeStatus(activationCode);
    activationCode.remainingDays = calculateRemainingDays(activationCode);

    return json({
      success: true,
      data: activationCode
    });

  } catch (err: any) {
    console.error("Error fetching activation code:", err);
    return error(500, {
      message: err.message || "获取激活码详情失败"
    });
  }
};

/**
 * 更新单个激活码
 */
export const PATCH: RequestHandler = async ({ request, params }) => {
  try {
    // 验证管理员权限
    await validateAdminAccess(request);

    const { id } = params;
    if (!id) {
      return error(400, {
        message: "缺少激活码ID"
      });
    }

    // 解析请求体
    const updateData: Partial<UpdateActivationCodeForm> = await request.json();

    const db = admin.firestore();
    const docRef = db.collection('activation_codes').doc(id);

    // 检查激活码是否存在
    const doc = await docRef.get();
    if (!doc.exists) {
      return error(404, {
        message: "激活码不存在"
      });
    }

    const currentData = doc.data()!;

    // 检查激活码是否已被使用
    if (currentData.activatedEmail) {
      return error(400, {
        message: "已使用的激活码不能修改"
      });
    }

    // 构建更新数据
    const updateFields: any = {};

    if (updateData.amount) {
      // 验证额度
      if (updateData.amount.agent < 0 || updateData.amount.completion < 0) {
        return error(400, {
          message: "额度不能为负数"
        });
      }
      updateFields.amount = updateData.amount;
    }

    if (updateData.validity !== undefined) {
      if (updateData.validity < 0) {
        return error(400, {
          message: "有效时限不能为负数"
        });
      }
      updateFields.validity = updateData.validity;
    }

    if (updateData.expireAt !== undefined) {
      const expireTime = new Date(updateData.expireAt).getTime();
      if (expireTime < Date.now() + 30 * 24 * 60 * 60 * 1000) {
        return error(400, {
          message: "过期时间不能小于1个月"
        });
      }
      updateFields.expireAt = expireTime;
    }

    if (updateData.remark !== undefined) {
      updateFields.remark = updateData.remark || null;
    }

    // 添加更新时间
    updateFields.updatedAt = Date.now();

    // 执行更新
    await docRef.update(updateFields);

    // 获取更新后的数据
    const updatedDoc = await docRef.get();
    const updatedData = updatedDoc.data()!;

    const activationCode: ActivationCode = {
      code: updatedDoc.id,
      amount: updatedData.amount,
      type: updatedData.type,
      isActive: updatedData.isActive,
      validity: updatedData.validity,
      createdAt: updatedData.createdAt,
      expireAt: updatedData.expireAt,
      activatedAt: updatedData.activatedAt,
      activatedEmail: updatedData.activatedEmail,
      remark: updatedData.remark,
      notForSale: updatedData.notForSale || false
    };

    activationCode.status = calculateActivationCodeStatus(activationCode);
    activationCode.remainingDays = calculateRemainingDays(activationCode);

    return json({
      success: true,
      message: "激活码更新成功",
      data: activationCode
    });

  } catch (err: any) {
    console.error("Error updating activation code:", err);
    return error(500, {
      message: err || err.message || "更新激活码失败"
    });
  }
};

/**
 * 删除单个激活码
 */
export const DELETE: RequestHandler = async ({ request, params }) => {
  try {
    // 验证管理员权限
    await validateAdminAccess(request);

    const { id } = params;
    if (!id) {
      return error(400, {
        message: "缺少激活码ID"
      });
    }

    const db = admin.firestore();
    const docRef = db.collection('activation_codes').doc(id);

    // 检查激活码是否存在
    const doc = await docRef.get();
    if (!doc.exists) {
      return error(404, {
        message: "激活码不存在"
      });
    }

    const data = doc.data()!;

    // 检查激活码是否已被使用
    if (data.activatedEmail) {
      return error(400, {
        message: "已使用的激活码不能删除"
      });
    }

    // 删除激活码
    await docRef.delete();

    return json({
      success: true,
      message: "激活码删除成功"
    });

  } catch (err: any) {
    console.error("Error deleting activation code:", err);
    return error(500, {
      message: err.message || "删除激活码失败"
    });
  }
};
