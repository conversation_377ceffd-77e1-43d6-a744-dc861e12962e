/**
 * 系统统计 API
 * 提供系统整体的用户和配额使用统计
 * 
 * 支持的操作：
 * - GET: 获取系统统计信息
 */

import type { RequestHandler } from '@sveltejs/kit';
import { json, error } from '@sveltejs/kit';
import { initializeFirebaseAdmin } from '$lib/firebase/firebase-admin';
import admin from 'firebase-admin';
import { env } from '$env/dynamic/private';
import type { SystemStats } from '$lib/types/user-quota.types';
import type { UserQuote, UserCompletionUsage } from '$lib/types/user-custom-claims.types';
import type { UserRecord } from 'firebase-admin/auth';

// 初始化 Firebase Admin
initializeFirebaseAdmin();

/**
 * 验证管理员权限
 */
async function validateAdminAccess(request: Request): Promise<string> {
  const userToken = request.headers.get("Authorization")?.replace("Bearer ", "").trim();
  
  if (!userToken) {
    throw new Error("未提供认证令牌");
  }

  try {
    const decodedToken = await admin.auth().verifyIdToken(userToken);
    const userEmail = decodedToken.email;

    if (!userEmail) {
      throw new Error("无法获取用户邮箱");
    }

    const adminEmail = env.ADMIN_EMAIL;
    if (!adminEmail || userEmail !== adminEmail) {
      throw new Error("您没有管理员权限");
    }

    return userEmail;
  } catch (err: any) {
    console.error("Admin validation failed:", err);
    throw new Error("权限验证失败");
  }
}

/**
 * 获取系统统计信息
 * 仅使用 userCustomClaims, 不查询 firestore.
 */
export const GET: RequestHandler = async ({ request }) => {
  try {
    // 验证管理员权限
    await validateAdminAccess(request);

    const auth = admin.auth();
    
    // 获取所有用户, 处理分页
    const users: UserRecord[] = [];
    let pageToken;
    do {
      const listUsersResult = await auth.listUsers(1000, pageToken);
      users.push(...listUsersResult.users);
      pageToken = listUsersResult.pageToken;
    } while (pageToken);

    // 计算基础统计
    const totalUsers = users.length;
    let totalQuotaDistributed = { agent: 0, completion: 0 };
    let totalQuotaUsed = { agent: 0, completion: 0 };
    let allTimeTokensConsumed = 0;
    const activeUserIds = new Set<string>();
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const allUsersStats: { uid: string; email: string; totalUsage: number; totalTokens: number }[] = [];

    // 统计配额分发和使用情况
    for (const user of users) {
      const customClaims = user.customClaims || {};
      const quotaPool: UserQuote[] = customClaims.quota_pool || [];
      const tokenUsage: UserCompletionUsage | undefined = customClaims.token_usage;

      // 统计配额
      if (quotaPool.length > 0) {
        quotaPool.forEach(quota => {
          totalQuotaDistributed.agent += quota.amount.agent;
          totalQuotaDistributed.completion += quota.amount.completion;
          totalQuotaUsed.agent += quota.used.agent;
          totalQuotaUsed.completion += quota.used.completion;
        });
      }

      // 统计Token使用
      if (tokenUsage) {
        allTimeTokensConsumed += tokenUsage.total_tokens || 0;

        // 活跃用户
        if (tokenUsage.last_used_at && new Date(tokenUsage.last_used_at).getTime() >= thirtyDaysAgo) {
          activeUserIds.add(user.uid);
        }

        // 用于计算Top用户
        allUsersStats.push({
          uid: user.uid,
          email: user.email || '',
          totalTokens: tokenUsage.total_tokens || 0,
          totalUsage: quotaPool.reduce((acc, q) => acc + q.used.agent + q.used.completion, 0)
        });
      }
    }

    const activeUsers = activeUserIds.size;

    // 计算平均Token使用量
    const averageTokensPerUser = totalUsers > 0 ? Math.round(allTimeTokensConsumed / totalUsers) : 0;

    // 获取使用量最高的用户（Top 10）
    const topUsers = allUsersStats
      .sort((a, b) => b.totalTokens - a.totalTokens)
      .slice(0, 10);

    // 构建系统统计信息
    const systemStats: SystemStats = {
      totalUsers,
      activeUsers,
      totalQuotaDistributed,
      totalQuotaUsed,
      totalTokensConsumed: allTimeTokensConsumed,
      averageTokensPerUser,
      topUsers
    };

    return json({
      success: true,
      data: systemStats
    });

  } catch (err: any) {
    console.error("Error fetching system stats:", err);
    return error(500, {
      message: err.message || "获取系统统计失败"
    });
  }
};