/**
 * 用户配额管理 API
 * 提供用户列表查询、配额信息获取、使用统计等功能
 * 
 * 支持的操作：
 * - GET: 获取所有用户及其配额信息
 */

import type { RequestHandler } from '@sveltejs/kit';
import { json, error } from '@sveltejs/kit';
import { initializeFirebaseAdmin } from '$lib/firebase/firebase-admin';
import admin from 'firebase-admin';
import { env } from '$env/dynamic/private';
import type {
  UserQuotaInfo,
  UserQuotaItem,
} from '$lib/types/user-quota.types';
import type { UserQuote, UserCompletionUsage } from '$lib/types/user-custom-claims.types';
import type { UserRecord } from 'firebase-admin/auth';

// 初始化 Firebase Admin
initializeFirebaseAdmin();

/**
 * 验证管理员权限
 */
async function validateAdminAccess(request: Request): Promise<string> {
  const userToken = request.headers.get("Authorization")?.replace("Bearer ", "").trim();
  
  if (!userToken) {
    throw new Error("未提供认证令牌");
  }

  try {
    const decodedToken = await admin.auth().verifyIdToken(userToken);
    const userEmail = decodedToken.email;

    if (!userEmail) {
      throw new Error("无法获取用户邮箱");
    }

    const adminEmail = env.ADMIN_EMAIL;
    if (!adminEmail || userEmail !== adminEmail) {
      throw new Error("您没有管理员权限");
    }

    return userEmail;
  } catch (err: any) {
    console.error("Admin validation failed:", err);
    throw new Error("权限验证失败");
  }
}

/**
 * 转换用户配额数据格式
 */
function convertUserQuotaFormat(quotaPool: UserQuote[]): UserQuotaItem[] {
  return quotaPool.map(quota => ({
    id: quota.id,
    type: quota.type,
    amount: quota.amount,
    used: quota.used,
    created_at: quota.created_at,
    expires_at: quota.expires_at,
    activated_at: quota.created_at
  }));
}

/**
 * 计算用户总配额信息
 */
function calculateTotalQuota(quotaPool: UserQuotaItem[]) {
  const totals = {
    agent: { total: 0, used: 0, remaining: 0 },
    completion: { total: 0, used: 0, remaining: 0 }
  };

  quotaPool.forEach(quota => {
    totals.agent.total += quota.amount.agent;
    totals.agent.used += quota.used.agent;
    totals.completion.total += quota.amount.completion;
    totals.completion.used += quota.used.completion;
  });

  totals.agent.remaining = totals.agent.total - totals.agent.used;
  totals.completion.remaining = totals.completion.total - totals.completion.used;

  return totals;
}

/**
 * 获取所有用户及其配额信息
 */
export const GET: RequestHandler = async ({ request }) => {
  try {
    // 验证管理员权限
    await validateAdminAccess(request);

    // 获取所有用户
    const auth = admin.auth();
    const allUsers: UserRecord[] = [];
    let pageToken;
    do {
      const listUsersResult = await auth.listUsers(1000, pageToken);
      allUsers.push(...listUsersResult.users);
      pageToken = listUsersResult.pageToken;
    } while (pageToken);

    // 构建用户配额信息
    const userQuotaInfos: UserQuotaInfo[] = allUsers.map(user => {
      const customClaims = user.customClaims || {};
      const quotaPool: UserQuote[] = customClaims.quota_pool || [];
      const tokenUsage: UserCompletionUsage | undefined = customClaims.token_usage;
      const convertedQuotaPool = convertUserQuotaFormat(quotaPool);

      return {
        uid: user.uid,
        email: user.email || '',
        displayName: user.displayName,
        emailVerified: user.emailVerified,
        photoURL: user.photoURL,
        createdAt: user.metadata.creationTime,
        lastLoginAt: user.metadata.lastSignInTime,
        disabled: user.disabled,
        quotaPool: convertedQuotaPool,
        totalQuota: calculateTotalQuota(convertedQuotaPool),
        lastUsedAt: tokenUsage?.last_used_at,
        totalTokens: tokenUsage?.total_tokens || 0,
      };
    });

    return json({
      success: true,
      data: userQuotaInfos
    });

  } catch (err: any) {
    console.error("Error fetching users:", err);
    return error(500, {
      message: err.message || "获取用户列表失败"
    });
  }
};
