/**
 * 用户配额管理操作 API
 * 提供用户配额的增加、减少、重置等管理操作
 * 
 * 支持的操作：
 * - POST: 执行配额操作（增加、减少、重置等）
 */

import type { RequestHandler } from '@sveltejs/kit';
import { json, error } from '@sveltejs/kit';
import { initializeFirebaseAdmin } from '$lib/firebase/firebase-admin';
import admin from 'firebase-admin';
import { env } from '$env/dynamic/private';
import {
  QuotaOperationType,
  type QuotaOperationRequest
} from '$lib/types/user-quota.types';
import type { UserQuote } from '$lib/types/user-custom-claims.types';
import { ActivationCodeType } from '$lib/types/activation-code.types';

// 初始化 Firebase Admin
initializeFirebaseAdmin();

/**
 * 验证管理员权限
 */
async function validateAdminAccess(request: Request): Promise<string> {
  const userToken = request.headers.get("Authorization")?.replace("Bearer ", "").trim();

  if (!userToken) {
    throw new Error("未提供认证令牌");
  }

  try {
    const decodedToken = await admin.auth().verifyIdToken(userToken);
    const userEmail = decodedToken.email;

    if (!userEmail) {
      throw new Error("无法获取用户邮箱");
    }

    const adminEmail = env.ADMIN_EMAIL;
    if (!adminEmail || userEmail !== adminEmail) {
      throw new Error("您没有管理员权限");
    }

    return userEmail;
  } catch (err: any) {
    console.error("Admin validation failed:", err);
    throw new Error("权限验证失败");
  }
}

/**
 * 生成管理员操作的配额ID
 */
function generateAdminQuotaId(): string {
  return `admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 执行配额操作
 */
export const POST: RequestHandler = async ({ request, params }) => {
  try {
    // 验证管理员权限
    const adminEmail = await validateAdminAccess(request);

    const { uid } = params;
    if (!uid) {
      return error(400, { message: "缺少用户ID参数" });
    }

    // 解析请求体
    const operationRequest: QuotaOperationRequest = await request.json();

    // 验证必填字段
    if (!operationRequest.operation || !operationRequest.quotaType) {
      return error(400, {
        message: "缺少必填字段：operation 和 quotaType"
      });
    }

    // 验证操作类型
    if (!Object.values(QuotaOperationType).includes(operationRequest.operation)) {
      return error(400, {
        message: "无效的操作类型"
      });
    }

    // 对于增加和减少操作，验证数量
    if ((operationRequest.operation === QuotaOperationType.ADD ||
      operationRequest.operation === QuotaOperationType.SUBTRACT) &&
      (!operationRequest.amount || operationRequest.amount <= 0)) {
      return error(400, {
        message: "增加或减少操作需要提供有效的数量"
      });
    }

    // 获取用户信息
    const auth = admin.auth();
    const user = await auth.getUser(uid);

    if (!user) {
      return error(404, { message: "用户不存在" });
    }

    // 获取当前配额池
    let quotaPool: UserQuote[] = user.customClaims?.quota_pool || [];

    // 执行操作
    let operationMessage = '';

    switch (operationRequest.operation) {
      case QuotaOperationType.ADD:
        // 增加配额 - 创建新的管理员配额项
        const newQuota: UserQuote = {
          id: generateAdminQuotaId(),
          type: ActivationCodeType.BONUS, // 管理员添加的配额默认为bonus类型
          amount: {
            agent: operationRequest.quotaType === 'agent' ? operationRequest.amount! : 0,
            completion: operationRequest.quotaType === 'completion' ? operationRequest.amount! : 0
          },
          used: {
            agent: 0,
            completion: 0
          },
          created_at: new Date().toISOString()
        };
        quotaPool.push(newQuota);
        operationMessage = `成功增加 ${operationRequest.quotaType} 配额 ${operationRequest.amount} 个`;
        break;

      case QuotaOperationType.SUBTRACT:
        // 减少配额 - 从现有配额中扣除
        let remainingToSubtract = operationRequest.amount!;

        // 优先从bonus类型的配额中扣除
        for (let i = quotaPool.length - 1; i >= 0 && remainingToSubtract > 0; i--) {
          const quota = quotaPool[i];
          if (quota.type === ActivationCodeType.BONUS) {
            const availableAmount = quota.amount[operationRequest.quotaType] - quota.used[operationRequest.quotaType];
            if (availableAmount > 0) {
              const toSubtract = Math.min(remainingToSubtract, availableAmount);
              quota.amount[operationRequest.quotaType] -= toSubtract;
              remainingToSubtract -= toSubtract;

              // 如果配额变为0，移除该配额项
              if (quota.amount.agent === 0 && quota.amount.completion === 0) {
                quotaPool.splice(i, 1);
              }
            }
          }
        }

        if (remainingToSubtract > 0) {
          return error(400, {
            message: `配额不足，只能减少 ${operationRequest.amount! - remainingToSubtract} 个`
          });
        }

        operationMessage = `成功减少 ${operationRequest.quotaType} 配额 ${operationRequest.amount} 个`;
        break;

      case QuotaOperationType.RESET:
        // 重置配额 - 将所有已使用的配额重置为0
        quotaPool.forEach(quota => {
          if (operationRequest.quotaType === 'agent') {
            quota.used.agent = 0;
          } else if (operationRequest.quotaType === 'completion') {
            quota.used.completion = 0;
          }
        });
        operationMessage = `成功重置 ${operationRequest.quotaType} 配额使用记录`;
        break;

      case QuotaOperationType.EXPIRE:
        // 过期配额 - 移除所有指定类型的配额
        quotaPool = quotaPool.filter(quota => {
          if (operationRequest.quotaType === 'agent') {
            return quota.amount.agent === 0;
          } else if (operationRequest.quotaType === 'completion') {
            return quota.amount.completion === 0;
          }
          return true;
        });
        operationMessage = `成功过期所有 ${operationRequest.quotaType} 配额`;
        break;

      default:
        return error(400, { message: "不支持的操作类型" });
    }

    // 更新用户的自定义声明
    await auth.setCustomUserClaims(uid, { quota_pool: quotaPool });

    // 记录操作日志（可选）
    const db = admin.firestore();
    const logRef = db.collection('admin_operation_logs').doc();
    await logRef.set({
      adminEmail,
      targetUserId: uid,
      targetUserEmail: user.email,
      operation: operationRequest.operation,
      quotaType: operationRequest.quotaType,
      amount: operationRequest.amount || 0,
      reason: operationRequest.reason || '',
      timestamp: Date.now(),
      result: 'success',
      message: operationMessage
    });

    return json({
      success: true,
      message: operationMessage,
      data: {
        operation: operationRequest.operation,
        quotaType: operationRequest.quotaType,
        amount: operationRequest.amount,
        newQuotaPool: quotaPool
      }
    });

  } catch (err: any) {
    console.error("Error executing quota operation:", err);

    // 记录错误日志
    try {
      const db = admin.firestore();
      const logRef = db.collection('admin_operation_logs').doc();
      await logRef.set({
        adminEmail: 'unknown',
        targetUserId: params.uid || 'unknown',
        operation: 'unknown',
        timestamp: Date.now(),
        result: 'error',
        error: err.message
      });
    } catch (logError) {
      console.error("Failed to log error:", logError);
    }

    return error(500, {
      message: err.message || "执行配额操作失败"
    });
  }
};
