/**
 * 单个用户详细信息 API
 * 提供用户详细配额信息、Token使用记录等
 * 
 * 支持的操作：
 * - GET: 获取用户详细信息和使用记录
 */

import type { RequestHandler } from '@sveltejs/kit';
import { json, error } from '@sveltejs/kit';
import { initializeFirebaseAdmin } from '$lib/firebase/firebase-admin';
import { env } from '$env/dynamic/private';
import type {
  UserQuotaInfo,
  UserUsageStats,
  TokenUsageRecord,
  UserQuotaItem,
  DateUsageStats
} from '$lib/types/user-quota.types';
import type { UserCompletionUsage, UserCustomClaims, UserQuote } from '$lib/types/user-custom-claims.types';
import type { Auth } from 'firebase-admin/auth';
import admin from 'firebase-admin';
import type { CollectionReference, DocumentData } from 'firebase-admin/firestore';
import { getDateString, getRecentTimeRange, getTimeRange } from '$lib/utils/datetime';
import { QuotaType } from '$lib/types/activation-code.types';

/**
 * 验证管理员权限
 */
async function validateAdminAccess(fireauth: Auth, request: Request): Promise<string> {
  const userToken = request.headers.get("Authorization")?.replace("Bearer ", "").trim();

  if (!userToken) {
    throw new Error("未提供认证令牌");
  }

  try {
    const decodedToken = await fireauth.verifyIdToken(userToken);
    const userEmail = decodedToken.email;

    if (!userEmail) {
      throw new Error("无法获取用户邮箱");
    }

    const adminEmail = env.ADMIN_EMAIL;
    if (!adminEmail || userEmail !== adminEmail) {
      throw new Error("您没有管理员权限");
    }

    return userEmail;
  } catch (err: any) {
    console.error("Admin validation failed:", err);
    throw new Error("权限验证失败");
  }
}

/**
 * 转换用户配额数据格式
 */
function convertUserQuotaFormat(quotaPool: UserQuote[]): UserQuotaItem[] {
  return quotaPool.map(quota => ({
    id: quota.id,
    type: quota.type,
    amount: quota.amount,
    used: quota.used,
    created_at: quota.created_at,
    expires_at: quota.expires_at,
    activated_at: quota.created_at
  }));
}

/**
 * 计算用户总配额信息
 */
function calculateTotalQuota(quotaPool: UserQuotaItem[]) {
  const totals = {
    agent: { total: 0, used: 0, remaining: 0 },
    completion: { total: 0, used: 0, remaining: 0 }
  };

  quotaPool.forEach(quota => {
    totals.agent.total += quota.amount.agent;
    totals.agent.used += quota.used.agent;
    totals.completion.total += quota.amount.completion;
    totals.completion.used += quota.used.completion;
  });

  totals.agent.remaining = totals.agent.total - totals.agent.used;
  totals.completion.remaining = totals.completion.total - totals.completion.used;

  return totals;
}

/**
 * 获取用户Token使用记录
 */
async function getUserTokenUsageRecords(collectionRef: CollectionReference<DocumentData>, uid: string, limit: number = 50): Promise<TokenUsageRecord[]> {
  const snapshot = await collectionRef
    .where('uid', '==', uid)
    .orderBy('createdAt', 'desc')
    .limit(limit)
    .get();

  return snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      uid: data.uid,
      email: data.email,
      used: data.used,
      model: data.model,
      quotaId: data.quotaId,
      tokenUsage: data.tokenUsage || [],
      createdAt: data.createdAt,
      totalTokens: data.totalTokens,
      promptTokens: data.promptTokens,
      completionTokens: data.completionTokens,
    } as TokenUsageRecord;
  });
}

const aggregateSpec = {
  count: admin.firestore.AggregateField.count(),
  used: admin.firestore.AggregateField.sum('used'),
  totalTokens: admin.firestore.AggregateField.sum('totalTokens'),
  promptTokens: admin.firestore.AggregateField.sum('promptTokens'),
  completionTokens: admin.firestore.AggregateField.sum('completionTokens'),
  promptAverage: admin.firestore.AggregateField.average('promptTokens'),
}

async function getTotalTokensForUser(collectionRef: CollectionReference<DocumentData>, uid: string, options: {
  createdAt?: {
    start?: number;
    end?: number;
  };
  type?: QuotaType[];
}) {
  try {
    const where = collectionRef
      .where('uid', '==', uid);
    if (options.createdAt) {
      if (options.createdAt.start) {
        where.where('createdAt', '>=', options.createdAt.start);
      }
      if (options.createdAt.end) {
        where.where('createdAt', '<=', options.createdAt.end);
      }
    }
    const query = [];
    if (options.type) {
      options.type.forEach(model => {
        query.push({
          model,
          where: where.where('model', '==', model),
        });
      });
    } else {
      query.push({
        model: "all",
        where,
      });
    }
    const snapshots = await Promise.all(
      query.map(async q => {
        const snapshot = await q.where
          .aggregate(aggregateSpec)
          .get();
        return {
          model: q.model,
          data: snapshot.data(),
        }
      }),
    )

    return {
      snapshots,
      tokens: snapshots.reduce((acc, snapshot) => acc + snapshot.data.totalTokens, 0),
    };
  } catch (error) {
    console.error(`获取用户 ${uid} tokens 时出错:`, error);
    return null;
  }
}

async function getTotalTokensForUserByRangeTime(collectionRef: CollectionReference<DocumentData>, uid: string, unit: 'day' | 'week' | 'month', n: number, type: 'recent' | 'period') {
  if (type === 'recent') {
    const timeAgo = getRecentTimeRange(unit, n);
    return await getTotalTokensForUser(collectionRef, uid, {
      createdAt: {
        start: timeAgo,
      },
      type: [QuotaType.AGENT, QuotaType.COMPLETION],
    });
  } else {
    let dateString = getDateString(unit, n);
    const range = getTimeRange(dateString, unit);
    if (!range) {
      throw new Error("Invalid date or unit.");
    }

    return await getTotalTokensForUser(collectionRef, uid, {
      createdAt: {
        start: range.start,
        end: range.end,
      },
      type: [QuotaType.AGENT, QuotaType.COMPLETION],
    });
  }
}

async function pushUsageStats(collectionRef: CollectionReference<DocumentData>, usage: DateUsageStats[], uid: string, unit: 'day' | 'week' | 'month', n: number, type: 'recent' | 'period') {
  const result = await getTotalTokensForUserByRangeTime(collectionRef, uid, unit, n, type);
  if (result) {
    usage.push({
      date: getDateString(unit, n),
      usage: result.snapshots.map(r => ({
        model: r.model,
        count: r.data.count,
        totalTokens: r.data.totalTokens,
        promptTokens: r.data.promptTokens,
        completionTokens: r.data.completionTokens,
        promptAverage: r.data.promptAverage,
      })),
      tokens: result.tokens,
    } as DateUsageStats);
  }
}

/**
 * 获取用户详细信息
 */
export const GET: RequestHandler = async ({ request, params }) => {
  try {

    // 初始化 Firebase Admin
    const { fireauth, firedb } = initializeFirebaseAdmin();

    // 验证管理员权限
    await validateAdminAccess(fireauth, request);

    const { uid } = params;
    if (!uid) {
      return error(400, { message: "缺少用户ID参数" });
    }

    // 获取用户基本信息
    const user = await fireauth.getUser(uid);
    const customClaims = (user.customClaims || {}) as UserCustomClaims;

    // 获取用户配额信息
    const quotaPool: UserQuote[] = customClaims.quota_pool || [];
    const tokenUsage = customClaims.token_usage || {
      total_tokens: 0,
      agent: {
        req_count: 0,
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
      },
      completion: {
        req_count: 0,
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
      },
      last_used_at: undefined,
    } as UserCompletionUsage;

    const convertedQuotaPool: UserQuotaItem[] = convertUserQuotaFormat(quotaPool);

    const collectionRef = firedb.collection('user_token_usages');

    // 获取Token使用记录
    const tokenRecords: TokenUsageRecord[]
      = await getUserTokenUsageRecords(collectionRef, uid, 10);

    const agentTokenUsage = tokenUsage.agent;
    const completionTokenUsage = tokenUsage.completion;
    const usageStats: UserUsageStats = {
      uid: user.uid,
      email: user.email || '',
      totalUsage: {
        agent: quotaPool.reduce((acc, q) => acc + q.used.agent, 0),
        completion: quotaPool.reduce((acc, q) => acc + q.used.completion, 0)
      },
      tokenStats: {
        totalTokens: tokenUsage.total_tokens,
        agent: {
          reqCount: agentTokenUsage.req_count,
          totalTokens: agentTokenUsage.total_tokens,
          promptTokens: agentTokenUsage.prompt_tokens,
          completionTokens: agentTokenUsage.completion_tokens,
        },
        completion: {
          reqCount: completionTokenUsage.req_count,
          totalTokens: completionTokenUsage.total_tokens,
          promptTokens: completionTokenUsage.prompt_tokens,
          completionTokens: completionTokenUsage.completion_tokens,
        },
      },
      recendUsage: [],
      dailyUsage: [],
    };

    // 构建用户详细信息
    const userQuotaInfo: UserQuotaInfo = {
      uid: user.uid,
      email: user.email || '',
      displayName: user.displayName,
      emailVerified: user.emailVerified,
      photoURL: user.photoURL,
      createdAt: user.metadata.creationTime,
      lastLoginAt: user.metadata.lastSignInTime,
      disabled: user.disabled,
      quotaPool: convertedQuotaPool,
      totalQuota: calculateTotalQuota(convertedQuotaPool),
      lastUsedAt: tokenUsage.last_used_at,
      totalTokens: tokenUsage.total_tokens,
    };

    return json({
      success: true,
      data: {
        userInfo: userQuotaInfo,
        usageStats,
        recentRecords: tokenRecords, // 返回最近记录
      }
    });

  } catch (err: any) {
    console.error("Error fetching user details:", err);
    return error(500, {
      message: err.message || "获取用户详细信息失败"
    });
  }
};