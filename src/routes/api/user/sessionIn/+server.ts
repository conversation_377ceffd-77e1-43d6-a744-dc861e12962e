import { initializeFirebaseAdmin } from "$lib/firebase/firebase-admin";
import { error, json, type <PERSON>quest<PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import { errorMessage } from "$lib/utils/errors";

/**
 * GET: Retrieves the current user's session information.
 * Verifies the session cookie and returns the user's decoded claims if valid.
 */
export const GET: RequestHandler = async ({ locals }) => {
    try {
        // The user's decoded token claims are populated in `locals.user` by the `hooks.server.ts` file.
        // We can simply check if it exists.
        const { user } = locals;

        if (!user) {
            // If `locals.user` is null, it means the user is not authenticated.
            return error(401, { message: "No authenticated user session found." });
        }

        // Return the user's data.
        return json({
            success: true,
            user,
        });
    } catch (e) {
        // Generic error handler for any unexpected issues.
        console.error("Error retrieving user session:", e);
        return error(500, { message: "Internal Server Error: " + errorMessage(e) });
    }
};

/**
 * POST: Creates a new session cookie from a Firebase ID token (Bearer token).
 * This is the login endpoint.
 */
export const POST: RequestHandler = async ({ request }) => {
    try {
        const { fireauth } = initializeFirebaseAdmin();
        const authorization = request.headers.get("Authorization");
        if (!authorization || !authorization.startsWith("Bearer ")) {
            return error(401, { message: "Missing or invalid Authorization header." });
        }
        const idToken = authorization.split("Bearer ")[1];

        const expiresIn = 60 * 60 * 24 * 5 * 1000; // 5 days

        // Ensure the ID token is valid before creating a session cookie.
        await fireauth.verifyIdToken(idToken);

        const sessionCookie = await fireauth.createSessionCookie(idToken, { expiresIn });

        const response = json({ status: "success" });
        response.headers.set("Set-Cookie", `__session=${sessionCookie}; HttpOnly; Path=/; Max-Age=${expiresIn / 1000}; Secure; SameSite=Strict`);

        return response;
    } catch (e) {
        console.error("Session sign-in error:", e);
        return error(500, { message: "Internal Server Error: " + errorMessage(e) });
    }
};

/**
 * DELETE: Logs the user out by revoking their session.
 * It invalidates the session cookie on the server and instructs the client to clear it.
 */
export const DELETE: RequestHandler = async ({ cookies, locals }) => {
    try {
        // Check if the user is authenticated by looking at `locals`.
        if (!locals.user) {
            return error(401, { message: "No active session to log out from." });
        }

        const { fireauth } = initializeFirebaseAdmin();

        // Revoke all refresh tokens for the user. This invalidates all their sessions.
        await fireauth.revokeRefreshTokens(locals.user.uid);

        // Instruct the browser to clear the session cookie.
        // We do this by setting the cookie with an expiration date in the past.
        cookies.set("__session", "", {
            path: "/",
            expires: new Date(0),
            httpOnly: true,
            secure: true,
            sameSite: 'strict'
        });

        return json({ status: "logged_out" });

    } catch (e) {
        console.error("Session sign-out error:", e);
        return error(500, { message: "Internal Server Error: " + errorMessage(e) });
    }
};