// src/routes/api/proxy/+server.ts
import { firebaseConfig } from '$lib/firebase/firebase';
import { ifetch } from '$lib/utils/ifetch';
import { error, type RequestHandler } from '@sveltejs/kit';

// https://cloud.google.com/identity-platform/docs/use-rest-api
const CORS_ALLOWED_ORIGINS = [
    "https://securetoken.googleapis.com",
    "https://identitytoolkit.googleapis.com",
];

export const POST: RequestHandler = async ({ request, url, fetch }) => {
    try {
        const searchParams = url.searchParams;
        const rest = searchParams.get('rest');

        // 参数验证
        if (!rest) {
            throw error(400, 'Missing required parameter "rest"');
        }

        // URL 验证和 CORS 检查
        let targetUrl: URL;
        try {
            targetUrl = new URL(rest);
        } catch {
            throw error(400, 'Invalid URL format in "rest" parameter');
        }

        // 安全检查：只允许 HTTPS 和特定的 HTTP（开发环境）
        if (targetUrl.protocol !== 'https:' &&
            !(targetUrl.protocol === 'http:' && targetUrl.hostname === 'localhost')) {
            throw error(403, 'Only HTTPS URLs are allowed');
        }

        // CORS 检查
        if (!CORS_ALLOWED_ORIGINS.includes(targetUrl.origin)) {
            throw error(403, `Origin ${targetUrl.origin} is not allowed`);
        }

        // 构建新的 URL
        searchParams.delete('rest');

        // API Key 验证
        if (!firebaseConfig.apiKey) {
            throw error(500, 'Firebase API key not configured');
        }

        searchParams.append('key', firebaseConfig.apiKey);

        const newUrl = new URL(targetUrl);
        newUrl.search = searchParams.toString();

        // 准备请求
        const method = request.method;
        const headers = new Headers();

        // 复制必要的请求头，但排除一些可能有问题的头
        const allowedHeaders = [
            'content-type',
            'authorization',
            'accept',
            'accept-language',
            'cache-control',
            "X-Firebase-Locale",
        ];

        for (const [key, value] of request.headers) {
            if (allowedHeaders.includes(key.toLowerCase())) {
                headers.set(key, value);
            }
        }

        // 处理请求体
        let body: BodyInit | undefined = undefined;
        const contentType = headers.get('content-type') || '';

        // 添加内容长度限制（例如 10MB）
        const contentLength = request.headers.get('content-length');
        if (contentLength && parseInt(contentLength) > 10 * 1024 * 1024) {
            throw error(413, 'Request body too large');
        }

        if (method !== 'GET' && method !== 'HEAD') {
            try {
                if (contentType.includes('application/json')) {
                    const data = await request.json();
                    body = JSON.stringify(data);
                } else if (contentType.includes('application/x-www-form-urlencoded')) {
                    body = await request.text();
                } else if (contentType.includes('multipart/form-data')) {
                    body = await request.formData();
                    // fetch 会自动设置正确的 content-type
                    headers.delete('content-type');
                } else if (contentType.includes('text/plain')) {
                    body = await request.text();
                } else {
                    // 对于其他类型，使用 arrayBuffer
                    body = await request.arrayBuffer();
                }
            } catch (parseError) {
                throw error(400, 'Invalid request body format: ' + JSON.stringify(parseError));
            }
        }

        // 设置请求超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

        try {
            // 发起代理请求
            const response = await ifetch(newUrl.toString(), {
                method,
                headers,
                body,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            // 检查响应状态
            if (!response.ok) {
                console.error(`Proxy request failed: ${response.status} ${response.statusText}`);
            }

            // 处理响应
            const responseBody = await response.arrayBuffer();

            // 构建响应头
            const responseHeaders = new Headers();

            // 复制必要的响应头
            const allowedResponseHeaders = [
                'content-type',
                'content-length',
                'cache-control',
                'expires',
                'last-modified',
                'etag'
            ];

            for (const [key, value] of response.headers) {
                if (allowedResponseHeaders.includes(key.toLowerCase())) {
                    responseHeaders.set(key, value);
                }
            }

            // 设置默认 content-type
            if (!responseHeaders.get('content-type')) {
                responseHeaders.set('content-type', 'application/octet-stream');
            }

            // 添加安全头
            responseHeaders.set('X-Content-Type-Options', 'nosniff');
            responseHeaders.set('X-Frame-Options', 'DENY');

            return new Response(responseBody, {
                status: response.status,
                statusText: response.statusText,
                headers: responseHeaders
            });

        } catch (fetchError: any) {
            clearTimeout(timeoutId);

            if (fetchError.name === 'AbortError') {
                throw error(408, 'Request timeout');
            }

            console.error('Proxy fetch error:', fetchError);
            throw error(502, 'Proxy request failed: ' + JSON.stringify(fetchError));
        }

    } catch (e) {
        // 如果是 SvelteKit error，直接抛出
        if (e && typeof e === 'object' && 'status' in e) {
            throw e;
        }

        // 其他未预期的错误
        console.error('Unexpected proxy error:', e);
        throw error(500, 'Internal server error: ' + JSON.stringify(e));
    }
};
