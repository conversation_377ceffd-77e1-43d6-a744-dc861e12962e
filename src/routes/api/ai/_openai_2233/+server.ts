import { env } from "$env/dynamic/private";
import { type RequestHand<PERSON> } from "@sveltejs/kit";
import { useQuota } from "$lib/firebase/firebase-admin";
import { chatCompletions } from "$lib/services/server.service";
import { QuotaType } from '$lib/types/activation-code.types';

export const POST: RequestHandler = async ({ request, url }) => {
    let controller = new AbortController();

    // 获取 Fetch API 的 AbortSignal
    const signal = request.signal;

    // 监听客户端中断请求
    signal.addEventListener('abort', () => {
        console.log('Client aborted the request.');
        controller.abort(); // 如果你有其他异步任务，可以中止
    });

    if (!env.GEMINI_API_KEY) {
        return new Response(JSON.stringify({
            success: false,
            error: "GEMINI_API_KEY not set"
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    const requestBody = await request.json();

    const token = request.headers.get("Authorization")?.replace("Bearer ", "").trim();

    if (!token || token === "") {
        return new Response(JSON.stringify({ success: false, error: "Unauthorized (102401)" }), { status: 401 });
    }

    try {
        const quotaType = requestBody.metadata?.quota_type as QuotaType || QuotaType.AGENT;
        delete requestBody.metadata;
        console.log('request', quotaType, requestBody.model, url.pathname + url.search);

        const response = await useQuota(token, quotaType, async () => {
            return await chatCompletions(controller.signal, requestBody);
        });
        return response;
    } catch (err: any) {
        if (err.name === "AbortError") {
            // 请求被取消
            console.log("服务端请求已取消");
        } else {
            console.log("服务端请求失败:", err);
        }
        return new Response(JSON.stringify({
            success: false,
            error: err.message || err.code,
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}