
import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

/**
 * Server-side load function for the admin layout.
 * This function is executed for every request to a page within the /admin/ directory.
 * Its primary responsibility is to protect the admin section from unauthorized access.
 *
 * @see https://kit.svelte.dev/docs/routing#layout-layout-server-ts
 */
export const load: PageServerLoad = async ({ locals }) => {
    // --- Admin Authorization Check ---

    // Retrieve the user data that was populated by the server hook (`hooks.server.ts`).
    const { user } = locals;

    // If the user is an authorized admin, the load function completes successfully.
    // The page requested by the user will be rendered.
    // We can optionally return data here to be made available to all admin pages.
    return {
        user: user // Pass the user object to the layout and its children pages.
    };
};
