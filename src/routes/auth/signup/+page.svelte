<script lang="ts">
  import { authService } from "$lib/services/auth.service";
  import { addToast } from "$lib/components/toast/toastStore";
  import AuthLayout from "$lib/components/auth/AuthLayout.svelte";
  import GoogleSignInButton from "$lib/components/auth/GoogleSignInButton.svelte";
  import PasswordStrength from "$lib/components/auth/PasswordStrength.svelte";
  import { Input } from "$lib/components/ui";
  import { Eye, EyeOff, UserPlus, Shield } from "@lucide/svelte";
  import { AuthMethod } from "$lib/auth/auth-strategy.types";
  import { InputValidator } from "$lib/utils/security";

  // 表单状态
  let email = $state("");
  let password = $state("");
  let confirmPassword = $state("");
  let showPassword = $state(false);
  let showConfirmPassword = $state(false);
  let isLoading = $state(false);
  let acceptTerms = $state(false);

  // 错误状态
  let emailError = $state("");
  let passwordError = $state("");
  let confirmPasswordError = $state("");
  let termsError = $state("");

  // 表单验证
  function validateForm(): boolean {
    emailError = "";
    passwordError = "";
    confirmPasswordError = "";
    termsError = "";

    let isValid = true;

    const emailValidation = InputValidator.validateEmail(email);
    if (!emailValidation.isValid) {
      emailError = emailValidation.error || "邮箱格式无效";
      isValid = false;
    }

    const passwordValidation = InputValidator.validatePassword(password);
    if (!passwordValidation.isValid) {
      passwordError = passwordValidation.errors.join("\n") || "密码强度不够";
      isValid = false;
    }

    // 确认密码验证
    if (!confirmPassword.trim()) {
      confirmPasswordError = "请确认密码";
      isValid = false;
    } else if (password !== confirmPassword) {
      confirmPasswordError = "两次输入的密码不一致";
      isValid = false;
    }

    // 服务条款验证
    if (!acceptTerms) {
      termsError = "请阅读并同意服务条款";
      isValid = false;
    }

    return isValid;
  }

  // 处理注册
  async function handleRegister(event: Event) {
    event.preventDefault();

    if (!validateForm() || isLoading) return;

    isLoading = true;
    try {
      const result = await authService.signUp(email, password);

      if (!result.success) {
        addToast({
          type: "error",
          message: result.toast?.message || "注册失败",
        });
      }
    } catch (error: any) {
      addToast({
        type: "error",
        message: error.message || "注册出现错误",
      });
    } finally {
      isLoading = false;
    }
  }

  // 切换密码显示
  function togglePasswordVisibility() {
    showPassword = !showPassword;
  }

  function toggleConfirmPasswordVisibility() {
    showConfirmPassword = !showConfirmPassword;
  }
</script>

<svelte:head>
  <title>注册 - 蘑菇🍄 AI小说</title>
  <meta name="description" content="注册蘑菇AI小说创作平台，开启您的创作之旅" />
</svelte:head>

<AuthLayout
  title="创建账户"
  subtitle="加入蘑菇AI小说，开启您的创作之旅"
  guard={{ requireAuth: false }}
>
  {#snippet children()}
    <!-- 注册表单 -->
    <form onsubmit={handleRegister} class="space-y-4">
      <!-- 邮箱输入 -->
      <div>
        <Input
          bind:value={email}
          type="email"
          label="邮箱地址"
          placeholder="请输入您的邮箱地址"
          error={emailError}
          required
          fullWidth
          autocomplete="email"
        />
      </div>

      <!-- 密码输入 -->
      <div class="relative">
        <Input
          bind:value={password}
          type={showPassword ? "text" : "password"}
          label="密码"
          placeholder="请输入密码（至少8个字符）"
          error={passwordError}
          required
          fullWidth
          autocomplete="new-password"
        />
        <button
          type="button"
          onclick={togglePasswordVisibility}
          class="absolute right-3 top-9 text-gray-400 hover:text-gray-600 transition-colors"
        >
          {#if showPassword}
            <EyeOff class="w-5 h-5" />
          {:else}
            <Eye class="w-5 h-5" />
          {/if}
        </button>
      </div>

      <!-- 密码强度指示器 -->
      <PasswordStrength {password} />

      <!-- 确认密码输入 -->
      <div class="relative">
        <Input
          bind:value={confirmPassword}
          type={showConfirmPassword ? "text" : "password"}
          label="确认密码"
          placeholder="请再次输入密码"
          error={confirmPasswordError}
          required
          fullWidth
          autocomplete="new-password"
        />
        <button
          type="button"
          onclick={toggleConfirmPasswordVisibility}
          class="absolute right-3 top-9 text-gray-400 hover:text-gray-600 transition-colors"
        >
          {#if showConfirmPassword}
            <EyeOff class="w-5 h-5" />
          {:else}
            <Eye class="w-5 h-5" />
          {/if}
        </button>
      </div>

      <!-- 服务条款同意 -->
      <div class="flex items-start space-x-3">
        <input
          type="checkbox"
          bind:checked={acceptTerms}
          id="terms"
          class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="terms" class="text-sm text-gray-700">
          我已阅读并同意
          <a
            href="/terms"
            target="_blank"
            class="text-blue-600 hover:text-blue-700 underline"
          >
            服务条款
          </a>
          和
          <a
            href="/privacy"
            target="_blank"
            class="text-blue-600 hover:text-blue-700 underline"
          >
            隐私政策
          </a>
        </label>
      </div>
      {#if termsError && !acceptTerms}
        <p class="text-sm text-red-600">{termsError}</p>
      {/if}

      <!-- 提交按钮 -->
      <button
        type="submit"
        disabled={isLoading}
        class="w-full flex items-center justify-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg mt-6"
      >
        {#if isLoading}
          <svg
            class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          注册中...
        {:else}
          <UserPlus class="w-4 h-4 mr-2" />
          创建账户
        {/if}
      </button>
    </form>

    <!-- 分隔线 -->
    <div class="relative my-6">
      <div class="absolute inset-0 flex items-center">
        <div class="w-full border-t border-gray-300"></div>
      </div>
      <div class="relative flex justify-center text-sm">
        <span class="px-2 bg-white text-gray-500">或</span>
      </div>
    </div>

    <!-- Google注册 -->
    <GoogleSignInButton
      text="使用 Google 注册"
      hidden={!authService.supportAuthMethod(AuthMethod.GOOGLE)}
    />

    <!-- 登录链接 -->
    <div class="text-center mt-6">
      <p class="text-sm text-gray-600">
        已有账户？
        <a
          href="/login"
          class="text-blue-600 hover:text-blue-700 font-medium transition-colors"
        >
          立即登录
        </a>
      </p>
    </div>

    <!-- 安全提示 -->
    <div class="mt-6 p-4 bg-green-50 rounded-lg">
      <div class="flex items-start">
        <Shield class="w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
        <div class="text-sm text-green-800">
          <p class="font-medium mb-1">您的数据安全</p>
          <p>
            我们采用 Google Firebase
            保护您的创作内容，绝不会泄露或滥用您的个人信息。
          </p>
        </div>
      </div>
    </div>
  {/snippet}
</AuthLayout>
