<script lang="ts">
  import { goto } from "$app/navigation";
  import { onMount } from "svelte";
  import { authService } from "$lib/services/auth.service";
  import { addToast } from "$lib/components/toast/toastStore";
  import AuthLayout from "$lib/components/auth/AuthLayout.svelte";
  import { Mail, RefreshCw, CheckCircle, ArrowLeft } from "@lucide/svelte";

  let isResending = $state(false);
  let resendCooldown = $state(0);
  let email = $state("");

  // 冷却定时器
  let cooldownTimer: ReturnType<typeof setInterval> | null = null;

  onMount(() => {
    email = authService.getCurrentUser()?.email || "";
    startTimer();
    return () => {
      if (cooldownTimer) {
        clearInterval(cooldownTimer);
      }
    };
  });

  function startTimer() {
    // 开始冷却倒计时（60秒）
    resendCooldown = 60;
    cooldownTimer = setInterval(() => {
      resendCooldown--;
      if (resendCooldown <= 0) {
        if (cooldownTimer) {
          clearInterval(cooldownTimer);
          cooldownTimer = null;
        }
      }
    }, 1000);
  }

  // 重新发送验证邮件
  async function resendVerificationEmail() {
    if (isResending || resendCooldown > 0) return;

    isResending = true;
    try {
      const success = await authService.sendEmailVerification();

      if (success) {
        addToast({
          type: "success",
          message: "验证邮件已重新发送，请查收",
        });

        startTimer();
      } else {
        addToast({
          type: "error",
          message: "发送验证邮件失败，请稍后重试",
        });
      }
    } catch (error: any) {
      addToast({
        type: "error",
        message: error.message || "发送验证邮件出现错误",
      });
    } finally {
      isResending = false;
    }
  }

  // 返回登录页
  function goToLogin() {
    authService.signOut();
  }

  // 检查验证状态
  async function checkVerificationStatus() {
    try {
      // 刷新用户状态
      await authService.reload();

      if (authService.isEmailVerified()) {
        addToast({
          type: "success",
          message: "邮箱验证成功！",
        });
        goto("/auth/activate");
      } else {
        addToast({
          type: "info",
          message: "邮箱尚未验证，请查收验证邮件",
        });
      }
    } catch (error: any) {
      addToast({
        type: "error",
        message: "检查验证状态失败",
      });
    }
  }
</script>

<svelte:head>
  <title>验证邮箱 - 蘑菇🍄 AI小说</title>
  <meta name="description" content="验证您的邮箱地址以完成注册" />
</svelte:head>

<AuthLayout
  title="验证您的邮箱"
  subtitle="我们已向您的邮箱发送了验证链接"
  guard={{ requireEmailVerified: false }}
>
  {#snippet children()}
    <div class="text-center space-y-6">
      <!-- 邮件图标 -->
      <div
        class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center"
      >
        <Mail class="w-8 h-8 text-blue-600" />
      </div>

      <!-- 说明文字 -->
      <div class="space-y-3">
        <p class="text-gray-700">验证邮件已发送至：</p>
        <p class="font-medium text-gray-900 bg-gray-50 px-4 py-2 rounded-lg">
          {email}
        </p>
        <p class="text-sm text-gray-600">
          请查收邮件并点击验证链接完成邮箱验证。如果没有收到邮件，请检查垃圾邮件文件夹。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="space-y-3">
        <!-- 重新发送按钮 -->
        <button
          onclick={resendVerificationEmail}
          disabled={isResending || resendCooldown > 0}
          class="w-full flex items-center justify-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          {#if isResending}
            <svg
              class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            发送中...
          {:else if resendCooldown > 0}
            <RefreshCw class="w-4 h-4 mr-2" />
            重新发送 ({resendCooldown}s)
          {:else}
            <RefreshCw class="w-4 h-4 mr-2" />
            重新发送验证邮件
          {/if}
        </button>

        <!-- 检查验证状态按钮 -->
        <button
          onclick={checkVerificationStatus}
          class="w-full flex items-center justify-center px-4 py-2.5 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200"
        >
          <CheckCircle class="w-4 h-4 mr-2" />
          我已验证，继续
        </button>

        <!-- 返回登录按钮 -->
        <button
          onclick={goToLogin}
          class="w-full flex items-center justify-center px-4 py-2.5 text-gray-600 font-medium rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
        >
          <ArrowLeft class="w-4 h-4 mr-2" />
          返回登录
        </button>
      </div>

      <!-- 提示信息 -->
      <div class="mt-8 p-4 bg-yellow-50 rounded-lg">
        <div class="text-sm text-yellow-800">
          <p class="font-medium mb-1">注意事项：</p>
          <ul class="list-disc list-inside space-y-1 text-left">
            <li>验证链接有效期为24小时</li>
            <li>如果长时间未收到邮件，请检查邮箱设置</li>
            <li>每天最多可发送5封验证邮件</li>
          </ul>
        </div>
      </div>
    </div>
  {/snippet}
</AuthLayout>
