<script lang="ts">
    import AiModelTestCard from "$lib/components/ai/AiModelTestCard.svelte";
    import { Button, Input } from "$lib/components/ui";
    import AutoHeightTextarea from "$lib/components/ui/AutoHeightTextarea.svelte";
    import { appModelsTestChats } from "$lib/stores/app.store";
    import { ChevronRight } from "@lucide/svelte";
    import { ChevronLeft } from "lucide-svelte";

    let count = $state(3);
    let prompt = $state(
        "你是网文小说资深作家, 请续写以下小说正文, 要求:\n1. 请续写小说正文\n2. 按照小说正文风格续写\n3. 请最多续写 100 字\n4. 只返回续写部分",
    );
    let text = $state("那一天, 世界发生了巨变, 无数植物从天空");

    let historySearch = $state("");

    $effect(() => {
        if (0 == historySearch.trim().length) {
            // 加载第一页
            appModelsTestChats.load();
        } else if (2 <= historySearch.trim().length) {
            // 搜索
            appModelsTestChats.search(historySearch);
        }
    });

    function onselectprompt(p: string) {
        prompt = p.trim();
    }
    function onselecttext(t: string) {
        text = t.trim();
    }
    function id(index: number) {
        return `diff-model_chat-${new Date().getTime()}${index}`;
    }
    function handleAddChat() {
        count = count + 1;
        requestAnimationFrame(() => {
            const element = document.getElementById("diff-model_chats");
            element?.lastElementChild?.scrollIntoView();
        });
    }
</script>

<div class="max-w-6xl mx-auto p-2 flex flex-col gap-4 justify-center">
    <div class="bg-blue-100 p-2 rounded-lg flex flex-row item-center">
        <h1 class="flex text-2xl font-bold">
            <a href="/" class="mx-2">🏠</a>
            ⚡️ <span class="hidden sm:block">
                测试不同模型不同参数的响应效果</span
            >
        </h1>
        <span class="flex-1"></span>
        <Button class="h-7" onclick={() => (count = count - 1)}>-</Button>
        <input class="mx-2 w-8 text-center" type="number" bind:value={count} />
        <Button class="h-7" onclick={handleAddChat}>+</Button>
    </div>
    <details>
        <summary>Prompt</summary>
        <AutoHeightTextarea flex="bg-gray-100" class="p-2" bind:value={prompt}
        ></AutoHeightTextarea>
    </details>
    <AutoHeightTextarea
        class="bg-gray-100 p-2 text-lg rounded-lg min-h-[5rem]"
        bind:value={text}
        placeholder="输入正文"
    ></AutoHeightTextarea>
    <div
        id="diff-model_chats"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
    >
        {#each Array(count) as v, index}
            <AiModelTestCard
                id={id(index)}
                {prompt}
                {text}
                open={true}
                {onselectprompt}
                {onselecttext}
            ></AiModelTestCard>
        {/each}
    </div>
    <details open>
        <summary>History ({$appModelsTestChats.total})</summary>
        <div class="flex items-center gap-2 my-2">
            <Input
                fullWidth={true}
                class="flex-1"
                type="search"
                placeholder="Search..."
                bind:value={historySearch}
            />
            <div class="flex items-center gap-1">
                <Button
                    variant="outline"
                    size="sm"
                    disabled={$appModelsTestChats.index <= 1}
                    onclick={appModelsTestChats.prev}
                    ><ChevronLeft></ChevronLeft>
                </Button>
                <span
                    >{$appModelsTestChats.index}/{$appModelsTestChats.pageCount}</span
                >
                <Button
                    variant="outline"
                    size="sm"
                    disabled={$appModelsTestChats.index >=
                        $appModelsTestChats.pageCount}
                    onclick={appModelsTestChats.next}
                    ><ChevronRight></ChevronRight>
                </Button>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {#each $appModelsTestChats.items as v}
                {#if v.id}
                    <AiModelTestCard
                        id={v.id}
                        {prompt}
                        {text}
                        {onselectprompt}
                        {onselecttext}
                    ></AiModelTestCard>
                {/if}
            {/each}
        </div>
    </details>
</div>
