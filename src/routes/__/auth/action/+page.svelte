<script lang="ts">
  import { goto } from "$app/navigation";
  import { page } from "$app/state";
  import { onMount } from "svelte";
  import { authService } from "$lib/services/auth.service";
  import Loading from "$lib/components/ui/Loading.svelte";
  import AuthLayout from "$lib/components/auth/AuthLayout.svelte";
  import { AlertCircle, CheckCircle } from "@lucide/svelte";

  let isProcessing = $state(true);
  let error = $state("");
  let success = $state("");
  let actionType = $state("");

  const mode = page.url.searchParams.get("mode");
  const oobCode = page.url.searchParams.get("oobCode");
  const apiKey = page.url.searchParams.get("apiKey");
  const continueUrl = page.url.searchParams.get("continueUrl");
  const lang = page.url.searchParams.get("lang");

  onMount(async () => {
    console.log("Auth action callback:", {
      mode,
      oobCode,
      apiKey,
      continueUrl,
      lang,
    });

    if (!mode || !oobCode) {
      error = "无效的认证链接";
      isProcessing = false;
      return;
    }

    try {
      const result = await authService.handleAuthCallback(mode, oobCode);

      if (result.redirectUrl) {
        goto(result.redirectUrl);
      } else {
        error = "无效的认证链接";
        isProcessing = false;
      }
    } catch (err: any) {
      console.error("Auth action error:", err);
      error = err.message || "处理认证操作时出现错误";
      isProcessing = false;
    }
  });

  function goToLogin() {
    goto("/login");
  }

  function goToHome() {
    goto("/");
  }
</script>

<svelte:head>
  <title>处理认证操作 - 蘑菇🍄 AI小说</title>
</svelte:head>

<AuthLayout
  title={actionType || "处理中"}
  subtitle="正在处理您的认证操作"
  showLogo={false}
  guard={{ ignoreAllCheck: true }}
>
  {#snippet children()}
    <div class="text-center space-y-6">
      {#if isProcessing}
        <!-- 处理中状态 -->
        <div
          class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center"
        >
          <Loading size="md" />
        </div>
        <p class="text-gray-600">正在处理 {actionType}...</p>
      {:else if error}
        <!-- 错误状态 -->
        <div
          class="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center"
        >
          <AlertCircle class="w-8 h-8 text-red-600" />
        </div>
        <div class="space-y-3">
          <h3 class="text-lg font-medium text-gray-900">操作失败</h3>
          <p class="text-red-600">{error}</p>
          <div class="space-y-2">
            <button
              onclick={goToLogin}
              class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              返回登录
            </button>
            <button
              onclick={goToHome}
              class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              返回首页
            </button>
          </div>
        </div>
      {:else if success}
        <!-- 成功状态 -->
        <div
          class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center"
        >
          <CheckCircle class="w-8 h-8 text-green-600" />
        </div>
        <div class="space-y-3">
          <h3 class="text-lg font-medium text-gray-900">操作成功</h3>
          <p class="text-green-600">{success}</p>
          <p class="text-sm text-gray-500">页面将自动跳转...</p>
        </div>
      {/if}
    </div>
  {/snippet}
</AuthLayout>
