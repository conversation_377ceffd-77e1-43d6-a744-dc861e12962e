
import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

/**
 * Server-side load function for the admin layout.
 * This function is executed for every request to a page within the /admin/ directory.
 * Its primary responsibility is to protect the admin section from unauthorized access.
 *
 * @see https://kit.svelte.dev/docs/routing#layout-layout-server-ts
 */
export const load: LayoutServerLoad = async ({ locals }) => {
    // --- Admin Authorization Check ---

    // Retrieve the user data that was populated by the server hook (`hooks.server.ts`).
    const { user } = locals;

    // Check for two conditions:
    // 1. Is there a user object? (i.e., is the user logged in?)
    // 2. Does the user object have the `admin` custom claim set to `true`?
    if (!user || !user.admin) {        
        // If either condition is false, the user is not an authorized admin.
        // In that case, we immediately stop further processing by throwing a redirect.
        // The user will be sent to the /login page.
        // The 303 status code is standard for "See Other", indicating that the client
        // should look at another URL for the response, which is appropriate after a POST or for this kind of redirection.
        throw redirect(303, '/login');
    }

    // If the user is an authorized admin, the load function completes successfully.
    // The page requested by the user will be rendered.
    // We can optionally return data here to be made available to all admin pages.
    return {
        user: user // Pass the user object to the layout and its children pages.
    };
};
