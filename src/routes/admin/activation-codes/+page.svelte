<script lang="ts">
  import { onMount } from "svelte";
  import { authService } from "$lib/services/auth.service";
  import { addToast } from "$lib/components/toast/toastStore";
  import { But<PERSON>, Card, Input, Badge, Loading } from "$lib/components/ui";
  import StatCard from "$lib/components/admin/StatCard.svelte";
  import {
    Plus,
    Search,
    Eye,
    Edit,
    Trash2,
    Download,
    RefreshCw,
    Package,
    Gift,
    Calendar,
    XCircle,
    Copy,
    Bot,
    FileText,
    Clock,
    CalendarDays,
    Hourglass,
    Mail,
    NotebookText,
  } from "@lucide/svelte";

  // 导入类型和工具函数
  import {
    type ActivationCode,
    type ActivationCodeStats,
    type CreateActivationCodeForm,
    ActivationCodeStatus,
    ActivationCodeType,
  } from "$lib/types/activation-code.types";

  import {
    generateActivationCode,
    calculateActivationCodeStatus,
    calculateRemainingDays,
    formatDate,
    formatRelativeTime,
    getActivationCodeTypeText,
    getActivationCodeStatusText,
    getActivationCodeStatusColor,
    getActivationCodeTypeColor,
    formatQuotaAmount,
    createDefaultQuotaAmount,
    getActivationCodeNotForSaleText,
    getActivationCodeNotForSaleColor,
  } from "$lib/utils/activation-code.utils";
  import Tooltip from "$lib/components/ui/Tooltip.svelte";
  import { User } from "lucide-svelte";

  // 状态管理
  let isLoading = $state(false);
  let activationCodes = $state<ActivationCode[]>([]);
  let currentPage = $state(1);
  let totalPages = $state(1);
  let searchTerm = $state("");
  let selectedType = $state<ActivationCodeType | "">("");
  let selectedStatus = $state<ActivationCodeStatus | "">("");
  let sortBy = $state<"createdAt" | "activatedAt" | "expireAt" | "validity">(
    "createdAt",
  );
  let sortOrder = $state<"asc" | "desc">("desc");

  // 模态框状态
  let showCreateModal = $state(false);
  let showEditModal = $state(false);
  let showDetailModal = $state(false);
  let selectedCode = $state<ActivationCode | null>(null);

  // 创建表单
  let createForm = $state<CreateActivationCodeForm>({
    code: "",
    amount: createDefaultQuotaAmount(),
    type: ActivationCodeType.BONUS,
    validity: 12,
    expireAt: new Date(new Date().setMonth(new Date().getMonth() + 1))
      .toISOString()
      .split("T")[0],
    remark: "",
    notForSale: true,
  });

  // 编辑表单
  let editForm = $state({
    id: "",
    amount: createDefaultQuotaAmount(),
    validity: 12,
    expireAt: "",
    remark: "",
    notForSale: false,
  });

  // 标签页
  let activeTab = $state("all");
  let tabs = $state([
    { id: "all", label: "全部", count: 0 },
    { id: "active", label: "可用", count: 0 },
    { id: "used", label: "已使用", count: 0 },
    { id: "expired", label: "已过期", count: 0 },
  ]);

  // 当选择奖励额度时，自动设置有效期为0且不可编辑, 否则恢复默认值
  $effect(() => {
    if (createForm.type === ActivationCodeType.BONUS) {
      createForm.validity = 0;
    } else {
      createForm.validity = 12;
    }
  });

  /**
   * 获取用户Token
   */
  async function getUserToken(): Promise<string> {
    return await authService.idToken();
  }

  /**
   * 获取用户Token
   */
  function getUserEmail(): string {
    const user = authService.getCurrentUser();
    return user?.email || "";
  }

  /**
   * 获取激活码列表
   */
  async function fetchActivationCodes(page: number = 1): Promise<void> {
    isLoading = true;

    try {
      const token = await getUserToken();
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
        ...(searchTerm && { search: searchTerm }),
        ...(selectedType && { type: selectedType }),
        ...(selectedStatus && { status: selectedStatus }),
        sortBy,
        sortOrder,
      });

      const response = await fetch(`/api/admin/activation-codes?${params}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("获取激活码列表失败");
      }

      const result = await response.json();
      activationCodes = result.data.items.map((code: ActivationCode) => ({
        ...code,
        status: calculateActivationCodeStatus(code),
        remainingDays: calculateRemainingDays(code),
      }));

      currentPage = result.data.pagination.page;
      totalPages = result.data.pagination.totalPages;
    } catch (error: any) {
      console.error("Error fetching activation codes:", error);
      addToast({
        type: "error",
        message: error.message || "获取激活码列表失败",
      });
    } finally {
      isLoading = false;
    }
  }

  /**
   * 创建激活码
   */
  async function createActivationCode(): Promise<void> {
    try {
      // 验证表单
      if (!createForm.amount.agent || !createForm.amount.completion) {
        addToast({
          type: "error",
          message: "请填写完整的额度信息",
        });
        return;
      }

      const token = await getUserToken();
      const response = await fetch("/api/admin/activation-codes", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(createForm),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "创建激活码失败");
      }

      const result = await response.json();
      addToast({
        type: "success",
        message: result.message || "激活码创建成功",
      });

      // 本地更新数据
      const newCode = {
        ...result.data,
        status: calculateActivationCodeStatus(result.data),
        remainingDays: calculateRemainingDays(result.data),
      };

      activationCodes = [newCode, ...activationCodes];

      // 重置表单并关闭模态框
      createForm = {
        code: "",
        amount: createDefaultQuotaAmount(),
        type: ActivationCodeType.BONUS,
        validity: 12,
        expireAt: new Date(new Date().setMonth(new Date().getMonth() + 1))
          .toISOString()
          .split("T")[0],
        remark: "",
        notForSale: false,
      };
      showCreateModal = false;
    } catch (error: any) {
      console.error("Error creating activation code:", error);
      addToast({
        type: "error",
        message: error.message || "创建激活码失败",
      });
    }
  }

  /**
   * 更新激活码
   */
  async function updateActivationCode(): Promise<void> {
    try {
      const token = await getUserToken();
      const response = await fetch(
        `/api/admin/activation-codes/${editForm.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            amount: editForm.amount,
            validity: editForm.validity,
            expireAt: editForm.expireAt,
            remark: editForm.remark,
            notForSale: editForm.notForSale,
          }),
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "更新激活码失败");
      }

      const result = await response.json();
      addToast({
        type: "success",
        message: "激活码更新成功",
      });

      // 本地更新数据
      const updatedCode = result.data;
      const index = activationCodes.findIndex(
        (c) => c.code === updatedCode.code,
      );
      if (index !== -1) {
        const oldCode = activationCodes[index];

        const newCode = {
          ...updatedCode,
          status: calculateActivationCodeStatus(updatedCode),
          remainingDays: calculateRemainingDays(updatedCode),
        };

        activationCodes[index] = newCode;
      }

      showEditModal = false;
      selectedCode = null;
    } catch (error: any) {
      console.error("Error updating activation code:", error);
      addToast({
        type: "error",
        message: error.message || "更新激活码失败",
      });
    }
  }

  /**
   * 删除激活码
   */
  async function deleteActivationCode(code: ActivationCode): Promise<void> {
    if (!confirm(`确定要删除激活码 "${code.code}" 吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const token = await getUserToken();
      const response = await fetch(`/api/admin/activation-codes/${code.code}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "删除激活码失败");
      }

      addToast({
        type: "success",
        message: "激活码删除成功",
      });

      // 本地更新数据
      const index = activationCodes.findIndex((c) => c.code === code.code);
      if (index !== -1) {
        activationCodes.splice(index, 1)[0];
      }
    } catch (error: any) {
      console.error("Error deleting activation code:", error);
      addToast({
        type: "error",
        message: error.message || "删除激活码失败",
      });
    }
  }

  /**
   * 复制到剪贴板
   */
  function copyToClipboard(text: string): void {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        addToast({
          type: "success",
          message: "已复制到剪贴板",
        });
      })
      .catch((err) => {
        console.error("Could not copy text: ", err);
        addToast({
          type: "error",
          message: "复制失败",
        });
      });
  }

  /**
   * 导出激活码
   */
  function exportActivationCodes(): void {
    const codes = activationCodes
      .map(
        (code) =>
          `${code.code}\t${getActivationCodeTypeText(code.type)}\t${formatQuotaAmount(code.amount)}\t${getActivationCodeStatusText(code.status!)}\t${formatDate(code.createdAt)}`,
      )
      .join("\n");

    const header = "激活码\t类型\t额度\t状态\t创建时间\n";
    const content = header + codes;

    const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `activation-codes-${new Date().toISOString().split("T")[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 生成新的激活码
   */
  function generateNewCode(): void {
    createForm.code = generateActivationCode();
  }

  /**
   * 打开编辑模态框
   */
  function openEditModal(code: ActivationCode): void {
    selectedCode = code;
    editForm = {
      id: code.code,
      amount: { ...code.amount },
      validity: code.validity,
      expireAt: new Date(code.expireAt).toISOString().split("T")[0],
      remark: code.remark || "",
      notForSale: code.notForSale || false,
    };
    showEditModal = true;
  }

  /**
   * 打开详情模态框
   */
  function openDetailModal(code: ActivationCode): void {
    selectedCode = code;
    showDetailModal = true;
  }

  /**
   * 处理搜索
   */
  function handleSearch(): void {
    currentPage = 1;
    fetchActivationCodes(1);
  }

  /**
   * 处理筛选
   */
  function handleFilter(): void {
    currentPage = 1;
    fetchActivationCodes(1);
  }

  /**
   * 处理标签页切换
   */
  function handleTabChange(tabId: string): void {
    activeTab = tabId;

    // 根据标签页设置状态筛选
    switch (tabId) {
      case "active":
        selectedStatus = ActivationCodeStatus.ACTIVE;
        break;
      case "used":
        selectedStatus = ActivationCodeStatus.USED;
        break;
      case "expired":
        selectedStatus = ActivationCodeStatus.EXPIRED;
        break;
      default:
        selectedStatus = "";
    }

    handleFilter();
  }

  /**
   * 刷新数据
   */
  async function refreshData(): Promise<void> {
    await Promise.all([fetchActivationCodes(currentPage)]);
  }

  // 页面加载时获取数据
  onMount(async () => {
    await Promise.all([fetchActivationCodes()]);
  });

  // 响应式计算过滤后的激活码
  const filteredCodes = $derived(activationCodes);
</script>

<svelte:head>
  <title>激活码管理 - 蘑菇🍄 AI小说</title>
  <meta
    name="description"
    content="现代化的激活码管理系统，支持奖励和订阅计划两种类型"
  />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
  <!-- 顶部导航栏 -->
  <div class="bg-white border-b border-gray-200 sticky top-0 z-10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div
              class="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center"
            >
              <Package class="w-5 h-5 text-white" />
            </div>
            <h1 class="text-xl font-bold text-gray-900 hidden sm:block">
              激活码管理
            </h1>
          </div>
          <Badge variant="secondary" class="text-xs">{getUserEmail()}</Badge>
          <Button
            variant="ghost"
            size="sm"
            href="/"
            class="text-gray-600 hover:text-gray-900"
          >
            /
          </Button>
          <Button
            variant="ghost"
            size="sm"
            href="/admin/user-quota"
            class="text-gray-600 hover:text-gray-900"
          >
            <User></User>
          </Button>
        </div>

        <div class="flex items-center space-x-2 sm:space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onclick={refreshData}
            class="text-gray-600 hover:text-gray-900"
          >
            <RefreshCw class="w-4 h-4" />
            <span class="ml-2 hidden md:block">刷新</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onclick={exportActivationCodes}
            class="text-gray-600 hover:text-gray-900"
          >
            <Download class="w-4 h-4" />
            <span class="ml-2 hidden md:block">导出</span>
          </Button>

          <Button
            variant="primary"
            size="sm"
            onclick={() => (showCreateModal = true)}
            class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            <Plus class="w-4 h-4" />
            <span class="ml-2 hidden sm:block">创建激活码</span>
          </Button>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 搜索和筛选栏 -->
    <Card class="mb-6">
      <div class="flex flex-wrap lg:flex-row gap-2">
        <!-- 筛选器 -->
        <select
          bind:value={selectedType}
          onchange={handleFilter}
          class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">所有类型</option>
          <option value={ActivationCodeType.BONUS}>奖励额度</option>
          <option value={ActivationCodeType.PLAN}>订阅计划</option>
        </select>

        <select
          bind:value={selectedStatus}
          onchange={handleFilter}
          class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">所有状态</option>
          <option value={ActivationCodeStatus.ACTIVE}>可用</option>
          <option value={ActivationCodeStatus.USED}>已使用</option>
          <option value={ActivationCodeStatus.EXPIRED}>已过期</option>
        </select>

        <!-- 搜索框 -->
        <div class="flex-1">
          <div class="relative w-full">
            <Search
              class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
            />
            <Input
              bind:value={searchTerm}
              placeholder="搜索激活码或备注..."
              class="pl-10 w-full"
              onkeydown={(e) => e.key === "Enter" && handleSearch()}
            />
          </div>
        </div>

        <Button variant="outline" onclick={handleSearch} class="px-4">
          <Search class="w-4 h-4" />
        </Button>
      </div>
    </Card>

    <!-- 标签页 -->
    <div class="mb-6">
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
          {#each tabs as tab}
            <button
              onclick={() => handleTabChange(tab.id)}
              class="py-2 px-1 border-b-2 font-medium text-sm transition-colors {activeTab ===
              tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}"
            >
              {tab.label}
              {#if tab.count > 0}
                <Badge variant="secondary" class="ml-2 text-xs">
                  {tab.count}
                </Badge>
              {/if}
            </button>
          {/each}
        </nav>
      </div>
    </div>

    <!-- 激活码列表 -->
    <Card>
      {#if isLoading}
        <div class="flex items-center justify-center py-12">
          <Loading text="加载中..." size="lg" />
        </div>
      {:else if filteredCodes.length === 0}
        <div class="text-center py-12">
          <Package class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">暂无激活码</h3>
          <p class="text-gray-500 mb-4">还没有创建任何激活码</p>
          <Button
            variant="primary"
            onclick={() => (showCreateModal = true)}
            class="bg-gradient-to-r from-purple-600 to-blue-600"
          >
            <Plus class="w-4 h-4 mr-2" />
            创建第一个激活码
          </Button>
        </div>
      {:else}
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
                >
                  激活码
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
                >
                  类型
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
                >
                  额度
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
                >
                  状态
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
                >
                  创建时间
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
                >
                  操作
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {#each filteredCodes as code (code.code)}
                <tr class="hover:bg-gray-50 transition-colors">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-2">
                      <div class="font-mono text-sm font-medium text-gray-900">
                        {code.code}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        class="text-gray-400 hover:text-gray-700"
                        onclick={() => copyToClipboard(code.code)}
                      >
                        <Copy class="w-4 h-4" />
                      </Button>
                    </div>
                    {#if code.remark}
                      <div
                        class="text-xs text-gray-500 mt-1 truncate max-w-xs"
                        title={code.remark}
                      >
                        {code.remark}
                      </div>
                    {/if}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge
                      variant="other"
                      class={getActivationCodeTypeColor(code.type)}
                    >
                      {getActivationCodeTypeText(code.type)}
                    </Badge>
                    <Badge
                      variant="other"
                      class={getActivationCodeNotForSaleColor(code.notForSale)}
                    >
                      {getActivationCodeNotForSaleText(code.notForSale)}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div class="flex items-center space-x-4">
                      <Tooltip text="AI 代理额度">
                        <div class="flex items-center text-gray-600">
                          <Bot class="w-4 h-4 mr-1.5 text-purple-500" />
                          <span class="font-semibold">{code.amount.agent}</span>
                        </div>
                      </Tooltip>
                      <Tooltip text="文本补全额度">
                        <div class="flex items-center text-gray-600">
                          <FileText class="w-4 h-4 mr-1.5 text-blue-500" />
                          <span class="font-semibold"
                            >{code.amount.completion}</span
                          >
                        </div>
                      </Tooltip>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge class={getActivationCodeStatusColor(code.status!)}>
                      {getActivationCodeStatusText(code.status!)}
                    </Badge>
                    {#if code.status === ActivationCodeStatus.ACTIVE && code.remainingDays !== undefined && code.remainingDays > 0}
                      <div class="text-xs text-gray-500 mt-1">
                        {code.remainingDays}天后过期
                      </div>
                    {:else if code.status === ActivationCodeStatus.USED}
                      <div class="text-xs text-gray-500 mt-1">
                        <Tooltip text={code.activatedEmail || ""}>
                          {code.activatedEmail?.split("@")[0]}
                        </Tooltip>
                      </div>
                    {/if}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      <Tooltip text={code.createdEmail}>
                        {code.createdEmail.split("@")[0]}
                      </Tooltip>
                    </div>
                    <div class="text-xs text-gray-500">
                      {formatRelativeTime(code.createdAt)}
                    </div>
                  </td>
                  <td
                    class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                  >
                    <div class="flex items-center justify-end space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onclick={() => openDetailModal(code)}
                        class="text-blue-600 hover:text-blue-900"
                      >
                        <Eye class="w-4 h-4" />
                      </Button>

                      {#if code.status === ActivationCodeStatus.ACTIVE}
                        <Button
                          variant="ghost"
                          size="sm"
                          onclick={() => openEditModal(code)}
                          class="text-green-600 hover:text-green-900"
                          disabled={true}
                        >
                          <Edit class="w-4 h-4" />
                        </Button>
                      {/if}

                      <Button
                        variant="ghost"
                        size="sm"
                        onclick={() => deleteActivationCode(code)}
                        class="text-red-600 hover:text-red-900"
                        disabled={code.status === ActivationCodeStatus.USED}
                      >
                        <Trash2 class="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {/if}
    </Card>

    <!-- 分页 -->
    {#if totalPages > 1}
      <div class="flex items-center justify-between mt-6">
        <div class="text-sm text-gray-700">
          第 {currentPage} 页，共 {totalPages} 页
        </div>
        <div class="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onclick={() => fetchActivationCodes(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            上一页
          </Button>
          <Button
            variant="outline"
            size="sm"
            onclick={() => fetchActivationCodes(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            下一页
          </Button>
        </div>
      </div>
    {/if}
  </div>
</div>

<!-- 创建激活码模态框 -->
{#if showCreateModal}
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
  >
    <Card class="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-gray-900">创建激活码</h3>
        <Button
          variant="ghost"
          size="sm"
          onclick={() => (showCreateModal = false)}
          class="text-gray-400 hover:text-gray-600"
        >
          <XCircle class="w-5 h-5" />
        </Button>
      </div>

      <form
        onsubmit={(e) => {
          e.preventDefault();
          createActivationCode();
        }}
        class="space-y-6"
      >
        <!-- 激活码 -->
        <div>
          <label
            for="create-code"
            class="block text-sm font-medium text-gray-700 mb-2"
          >
            激活码
          </label>
          <div class="w-full flex gap-2">
            <Input
              id="create-code"
              bind:value={createForm.code}
              placeholder="留空自动生成，或手动输入"
            />
            <Button type="button" variant="outline" onclick={generateNewCode}>
              生成
            </Button>
          </div>
          <p class="text-xs text-gray-500 mt-1">
            激活码最多20位，包含大小写字母和数字
          </p>
        </div>

        <!-- 是否为非卖品 -->
        <div class="pt-2">
          <label
            for="create-notForSale"
            class="flex items-center space-x-3 cursor-pointer"
          >
            <input
              id="create-notForSale"
              type="checkbox"
              bind:checked={createForm.notForSale}
              class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
            />
            <span class="text-sm font-medium text-gray-700"
              >非卖品 (Not for Sale)*
            </span>
          </label>
          <p class="text-xs text-gray-500 mt-1 pl-7">
            标记为非卖品的激活码仅用于特殊用途，不会在正常渠道出售。
          </p>
        </div>

        <!-- 类型 -->
        <div>
          <div class="block text-sm font-medium text-gray-700 mb-2">类型 *</div>
          <div class="grid grid-cols-2 gap-4">
            <label
              class="flex items-center p-4 border rounded-lg cursor-pointer {createForm.type ===
              ActivationCodeType.BONUS
                ? 'border-purple-500 bg-purple-50'
                : 'border-gray-300 hover:border-gray-400'}"
            >
              <input
                type="radio"
                bind:group={createForm.type}
                value={ActivationCodeType.BONUS}
                class="sr-only"
              />
              <div class="flex items-center">
                <Gift class="w-5 h-5 text-purple-600 mr-3" />
                <div>
                  <div class="font-medium text-gray-900">奖励额度</div>
                  <div class="text-sm text-gray-500">一次性额度，不会重置</div>
                </div>
              </div>
            </label>

            <label
              class="flex items-center p-4 border rounded-lg cursor-pointer {createForm.type ===
              ActivationCodeType.PLAN
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'}"
            >
              <input
                type="radio"
                bind:group={createForm.type}
                value={ActivationCodeType.PLAN}
                class="sr-only"
              />
              <div class="flex items-center">
                <Calendar class="w-5 h-5 text-blue-600 mr-3" />
                <div>
                  <div class="font-medium text-gray-900">订阅计划</div>
                  <div class="text-sm text-gray-500">每月重置，会过期</div>
                </div>
              </div>
            </label>
          </div>
        </div>

        <!-- 额度设置 -->
        <div>
          <div class="block text-sm font-medium text-gray-700 mb-2">
            额度设置 *
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label
                for="create-amount-agent"
                class="block text-xs text-gray-600 mb-1">AI 代理额度</label
              >
              <Input
                id="create-amount-agent"
                type="number"
                bind:value={createForm.amount.agent}
                min="0"
                required
                placeholder="30"
              />
            </div>
            <div>
              <label
                for="create-amount-completion"
                class="block text-xs text-gray-600 mb-1">文本补全额度</label
              >
              <Input
                id="create-amount-completion"
                type="number"
                bind:value={createForm.amount.completion}
                min="0"
                required
                placeholder="200"
              />
            </div>
          </div>
        </div>

        <!-- 有效期设置 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label
              for="create-validity"
              class="block text-sm font-medium text-gray-700 mb-2"
            >
              有效时限（月）*
            </label>
            <Input
              id="create-validity"
              type="number"
              bind:value={createForm.validity}
              min="0"
              required
              placeholder="12"
              disabled={createForm.type === ActivationCodeType.BONUS}
            />
            <p class="text-xs text-gray-500 mt-1">
              从激活时开始计算，0表示不限制
            </p>
          </div>
          <div>
            <label
              for="create-expireAt"
              class="block text-sm font-medium text-gray-700 mb-2"
            >
              过期时间 *
            </label>
            <Input
              id="create-expireAt"
              type="date"
              bind:value={createForm.expireAt}
              required
            />
            <p class="text-xs text-gray-500 mt-1">
              未使用的激活码将在此日期后失效
            </p>
          </div>
        </div>

        <!-- 备注 -->
        <div>
          <label
            for="create-remark"
            class="block text-sm font-medium text-gray-700 mb-2"
          >
            备注
          </label>
          <textarea
            id="create-remark"
            bind:value={createForm.remark}
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="可选的备注信息..."
          ></textarea>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onclick={() => (showCreateModal = false)}
          >
            取消
          </Button>
          <Button
            type="submit"
            variant="primary"
            class="bg-gradient-to-r from-purple-600 to-blue-600"
          >
            创建激活码
          </Button>
        </div>
      </form>
    </Card>
  </div>
{/if}

<!-- 编辑激活码模态框 -->
{#if showEditModal && selectedCode}
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
  >
    <Card class="w-full max-w-lg max-h-[90vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-gray-900">编辑激活码</h3>
        <Button
          variant="ghost"
          size="sm"
          onclick={() => (showEditModal = false)}
          class="text-gray-400 hover:text-gray-600"
        >
          <XCircle class="w-5 h-5" />
        </Button>
      </div>

      <form
        onsubmit={(e) => {
          e.preventDefault();
          updateActivationCode();
        }}
        class="space-y-6"
      >
        <!-- 激活码（只读） -->
        <div>
          <label
            for="edit-code"
            class="block text-sm font-medium text-gray-700 mb-2"
          >
            激活码
          </label>
          <Input
            id="edit-code"
            value={selectedCode.code}
            readonly
            class="bg-gray-50"
          />
        </div>

        <!-- 额度设置 -->
        <div>
          <div class="block text-sm font-medium text-gray-700 mb-2">
            额度设置
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label
                for="edit-amount-agent"
                class="block text-xs text-gray-600 mb-1">AI 代理额度</label
              >
              <Input
                id="edit-amount-agent"
                type="number"
                bind:value={editForm.amount.agent}
                min="0"
                required
              />
            </div>
            <div>
              <label
                for="edit-amount-completion"
                class="block text-xs text-gray-600 mb-1">文本补全额度</label
              >
              <Input
                id="edit-amount-completion"
                type="number"
                bind:value={editForm.amount.completion}
                min="0"
                required
              />
            </div>
          </div>
        </div>

        <!-- 有效期设置 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label
              for="edit-validity"
              class="block text-sm font-medium text-gray-700 mb-2"
            >
              有效时限（月）
            </label>
            <Input
              id="edit-validity"
              type="number"
              bind:value={editForm.validity}
              min="0"
              required
            />
          </div>
          <div>
            <label
              for="edit-expireAt"
              class="block text-sm font-medium text-gray-700 mb-2"
            >
              过期时间
            </label>
            <Input
              id="edit-expireAt"
              type="date"
              bind:value={editForm.expireAt}
              required
            />
          </div>
        </div>

        <!-- 备注 -->
        <div>
          <label
            for="edit-remark"
            class="block text-sm font-medium text-gray-700 mb-2"
          >
            备注
          </label>
          <textarea
            id="edit-remark"
            bind:value={editForm.remark}
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="可选的备注信息..."
          ></textarea>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onclick={() => (showEditModal = false)}
          >
            取消
          </Button>
          <Button
            type="submit"
            variant="primary"
            class="bg-gradient-to-r from-green-600 to-blue-600"
          >
            保存更改
          </Button>
        </div>
      </form>
    </Card>
  </div>
{/if}

<!-- 详情模态框 -->
{#if showDetailModal && selectedCode}
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
  >
    <Card class="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
      <div class="flex justify-between items-start mb-4">
        <div>
          <h3 class="text-xl font-semibold text-gray-900">激活码详情</h3>
          <p class="text-sm text-gray-500">查看激活码的详细信息和状态</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onclick={() => (showDetailModal = false)}
          class="text-gray-400 hover:text-gray-600"
        >
          <XCircle class="w-5 h-5" />
        </Button>
      </div>

      <div class="space-y-6">
        <!-- Code and Status -->
        <div class="p-4 bg-gray-50 rounded-lg">
          <div class="flex items-center justify-between">
            <span
              class="font-mono text-2xl font-bold text-gray-800 tracking-wider"
              >{selectedCode.code}</span
            >
            <Button
              variant="outline"
              size="sm"
              class="text-gray-600"
              onclick={() => copyToClipboard(selectedCode?.code!)}
            >
              <Copy class="w-6 h-6" />
            </Button>
          </div>
          <div class="mt-3 flex items-center space-x-2">
            <Badge
              variant="other"
              class={getActivationCodeStatusColor(selectedCode.status!)}
              size="lg"
            >
              {getActivationCodeStatusText(selectedCode.status!)}
            </Badge>
            <Badge
              variant="other"
              class={getActivationCodeTypeColor(selectedCode.type)}
              size="lg"
            >
              {getActivationCodeTypeText(selectedCode.type)}
            </Badge>
            <Badge
              variant="other"
              class={getActivationCodeNotForSaleColor(selectedCode.notForSale)}
              size="lg"
            >
              {getActivationCodeNotForSaleText(selectedCode.notForSale)}
            </Badge>
            <Badge variant="default" size="lg">
              <Hourglass class="w-5 h-5 text-yellow-500 mr-3 flex-shrink-0" />
              {selectedCode.validity === 0
                ? "不限制"
                : `${selectedCode.validity}个月`}
            </Badge>
          </div>
        </div>

        <div class="grid md:grid-cols-2 gap-x-8 gap-y-6 pt-2">
          <!-- Quota -->
          <div class="space-y-4">
            <div class="flex items-start">
              <Bot class="w-5 h-5 text-purple-500 mr-3 flex-shrink-0" />
              <div class="flex-1">
                <p class="text-sm text-gray-500">AI 代理额度</p>
                <p class="text-base font-semibold text-gray-800">
                  {selectedCode.amount.agent}
                </p>
              </div>
            </div>
            <div class="flex items-start">
              <FileText class="w-5 h-5 text-blue-500 mr-3 flex-shrink-0" />
              <div class="flex-1">
                <p class="text-sm text-gray-500">文本补全额度</p>
                <p class="text-base font-semibold text-gray-800">
                  {selectedCode.amount.completion}
                </p>
              </div>
            </div>
          </div>

          <!-- Validity -->
          <div class="space-y-4">
            <div class="flex items-start">
              <CalendarDays class="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
              <div class="flex-1">
                <p class="text-sm text-gray-500">过期时间</p>
                <p class="text-base font-semibold text-gray-800">
                  {formatDate(selectedCode.expireAt)}
                </p>
              </div>
            </div>
            <div class="flex items-start">
              <Clock class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
              <div class="flex-1">
                <p class="text-sm text-gray-500">剩余天数</p>
                <p class="text-base font-semibold text-gray-800">
                  {#if selectedCode.status !== ActivationCodeStatus.ACTIVE}
                    -
                  {:else if selectedCode.remainingDays === undefined}
                    -
                  {:else if selectedCode.remainingDays < 0}
                    已过期
                  {:else}
                    {selectedCode.remainingDays} 天
                  {/if}
                </p>
              </div>
            </div>
          </div>

          <!-- Timeline -->
          <div class="space-y-4">
            <div class="flex items-start">
              <CalendarDays class="w-5 h-5 text-cyan-500 mr-3 flex-shrink-0" />
              <div class="flex-1">
                <p class="text-sm text-gray-500">创建时间</p>
                <p class="text-base font-semibold text-gray-800">
                  {formatDate(selectedCode.createdAt)}
                </p>
              </div>
            </div>
            <div class="flex items-start">
              <Mail class="w-5 h-5 text-pink-500 mr-3 flex-shrink-0" />
              <div class="flex-1">
                <p class="text-sm text-gray-500">创建邮箱</p>
                <p class="text-base font-semibold text-gray-800">
                  {selectedCode.createdEmail}
                </p>
              </div>
            </div>
          </div>

          <!-- Activation Info -->
          <div class="space-y-4">
            <div class="flex items-start">
              <CalendarDays
                class="w-5 h-5 text-indigo-500 mr-3 flex-shrink-0"
              />
              <div class="flex-1">
                <p class="text-sm text-gray-500">激活时间</p>
                <p class="text-base font-semibold text-gray-800">
                  {selectedCode.activatedAt
                    ? formatDate(selectedCode.activatedAt)
                    : "未激活"}
                </p>
              </div>
            </div>
            {#if selectedCode.activatedEmail}
              <div class="flex items-start">
                <Mail class="w-5 h-5 text-pink-500 mr-3 flex-shrink-0" />
                <div class="flex-1">
                  <p class="text-sm text-gray-500">激活邮箱</p>
                  <p class="text-base font-semibold text-gray-800">
                    {selectedCode.activatedEmail}
                  </p>
                </div>
              </div>
            {/if}
          </div>
        </div>

        <!-- Remark -->
        {#if selectedCode.remark}
          <div class="pt-4 border-t">
            <div class="flex items-start">
              <NotebookText
                class="w-5 h-5 text-gray-500 mr-3 mt-0.5 flex-shrink-0"
              />
              <div class="flex-1">
                <p class="text-sm text-gray-500">备注</p>
                <p
                  class="text-base text-gray-800 bg-gray-50 p-3 rounded-md mt-1 whitespace-pre-wrap"
                >
                  {selectedCode.remark}
                </p>
              </div>
            </div>
          </div>
        {/if}
      </div>
    </Card>
  </div>
{/if}
