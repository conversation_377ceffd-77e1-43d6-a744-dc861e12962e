<script lang="ts">
    import type { KeyboardShortcut } from "$lib/actions/window";
    import AutoGenerateButton from "$lib/components/buttons/AutoGenerateButton.svelte";
    import { addToast } from "$lib/components/toast/toastStore";
    import { <PERSON><PERSON> } from "$lib/components/ui";
    import AutoHeightTextarea from "$lib/components/ui/AutoHeightTextarea.svelte";
    import Card from "$lib/components/ui/Card.svelte";
    import EmptyState from "$lib/components/ui/EmptyState.svelte";
    import Loading from "$lib/components/ui/Loading.svelte";
    import Sticky from "$lib/components/ui/Sticky.svelte";
    import Tabs from "$lib/components/ui/Tabs.svelte";
    import { sendLiteOpenaiRequest } from "$lib/services/openai.service";
    import { Minus, Plus } from "lucide-svelte";
    import { onMount } from "svelte";
    import { authService } from "$lib/auth/auth-unified.service";
    import { QuotaType } from "$lib/types/activation-code.types";
    import { goto } from "$app/navigation";

    export const data;

    let timer: any;
    let text = $state("");
    let suggestion = $state("");
    let isSuggesting = $state(false);
    let autofocus = $state(false);
    let selectrange: any = $state(null);

    let novelSettings: any = $state({});
    let newSettings: any = $state({});
    let newSettingLoading: any = $state({});

    let controller = new AbortController();

    let tabs = $state([
        {
            id: "characters",
            label: "角色",
            icon: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg>',
            badge: novelSettings["characters"]?.length || 0,
        },
        {
            id: "factions",
            label: "阵营",
            icon: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
            badge: novelSettings["factions"]?.length || 0,
        },
        {
            id: "outlines",
            label: "大纲",
            icon: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path></svg>',
            badge: novelSettings["outlines"]?.length || 0,
        },
        {
            id: "timelines",
            label: "时间线",
            icon: '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg>',
            badge: novelSettings["timelines"]?.length || 0,
        },
        {
            id: "storylines",
            label: "故事线",
            // 故事线 svg icon
            icon: ' <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"></path></svg>',
            badge: novelSettings["storylines"]?.length || 0,
        },
        {
            // 世界观
            id: "theworld",
            label: "世界观",
            icon: "🌍",
            badge: novelSettings["theworld"]?.length || 0,
        },
        {
            id: "storystyle",
            label: "叙事风格",
            // 叙事风格 svg icon
            icon: "😊",
            badge: novelSettings["storystyle"]?.length || 0,
        },
    ]);

    // 添加快捷键操作
    const shortcuts: KeyboardShortcut[] = [
        {
            key: "Tab",
            handle: () => {
                if (suggestion) {
                    handleKeyTab();
                    return true;
                }
                return false;
            },
        },
        {
            key: "Escape",
            handle: () => {
                tryAbortRequestAiAutoGenerate();

                if (suggestion) {
                    handleKeyEsc();
                    return true;
                }
                return false;
            },
        },
    ];

    async function autoAiGenerate() {
        if (!text || text.trim().length < 3) {
            return;
        }

        isSuggesting = true;

        // setTimeout(() => {
        //     suggestion = text + ",more.\n";
        //     isSuggesting = false;
        // }, 2000);

        // return;

        try {
            controller = new AbortController();
            const content = await sendLiteOpenaiRequest(
                controller.signal,
                QuotaType.COMPLETION,
                [joinNovelSettings(), `# 小说正文:\n\n${text}`],
                "你是网文小说资深作家, 请根据小说基本设定, 续写以下小说正文, 要求:\n1. 请续写小说正文\n2. 按照小说正文风格续写\n3. 请最多续写 100 字\n4. 只返回续写部分",
            );

            console.log(content);

            suggestion = text + content;
        } catch (err: any) {
            handleRequestError(err, "AiContinued");
        } finally {
            isSuggesting = false;
        }
    }

    function handleRequestError(err: any, ref: string) {
        if (err.name === "AbortError") {
            // 请求被取消
            console.log(ref, "请求已取消");
        } else {
            const msg: string = err.message;
            if (msg.includes("Invalid activation code")) {
                addToast({
                    message: "激活码无效\n请在设置里输入正确的激活码",
                    type: "error",
                });
            } else {
                addToast({
                    message: "请求失败\n" + err.message,
                    type: "error",
                });
            }
            console.log(ref, "error:", err);
        }
    }

    function tryAbortRequestAiAutoGenerate() {
        if (isSuggesting) {
            controller.abort();
            isSuggesting = false;
        }
    }

    function joinNovelSettings() {
        return (
            "# 小说基本设定:\n\n" +
            tabs
                .map(
                    (tab) =>
                        `## ${tab.label}\n` +
                        novelSettings[tab.id].join("\n------\n"),
                )
                .join("\n\n")
        );
    }

    function handleInput() {
        tryAbortRequestAiAutoGenerate();
        clearTimeout(timer);
        suggestion = "";
        timer = setTimeout(() => {
            autoAiGenerate();
        }, 3000);
    }

    function handleSelectionchage(e: Event) {
        if (!suggestion || isSuggesting) {
            const node = e.target || e.srcElement;
            if (
                node instanceof HTMLInputElement ||
                node instanceof HTMLTextAreaElement
            ) {
                const start = node.selectionStart;
                const end = node.selectionEnd;
                // oninput 在 onselectionchage 之前触发,
                // 所以这里需要判断一下, 只有移动光标才会触发取消续写操作
                if (start != end || (start != text.length && start != 0)) {
                    // console.log("selectionchange", start, end);
                    clearTimeout(timer);
                }
            }
        }
    }

    function handleKeyTab() {
        text = suggestion;
        suggestion = "";
        autofocus = true;
        selectrange = { start: text.length, end: text.length };
        handleInput();
    }

    function handleKeyEsc() {
        suggestion = "";
        selectrange = { start: text.length, end: text.length };
    }

    async function handleAutoAiGen(tab: any) {
        if (newSettings[tab.id].trim().length == 0) {
            return;
        }
        newSettingLoading[tab.id] = true;
        try {
            const content = await sendLiteOpenaiRequest(
                controller.signal,
                QuotaType.AGENT,
                [
                    joinNovelSettings(),
                    `# ${tab.label}01: ${newSettings[tab.id]}\n\n------\n# 要求:\n1. 请根据小说基本设定和当前${tab.label}01的设定, 生成补全并生成吸引人的${tab.label}01设定\n2. 请最多 100 字\n3. 只返回当前${tab.label}01的信息\n4. 返回格式: 纯文本`,
                ],
            );

            newSettings[tab.id] = content;
        } catch (err: any) {
            handleRequestError(err, "AiGen");
        } finally {
            newSettingLoading[tab.id] = false;
        }
    }

    function handleAddSetting(tabId: string) {
        novelSettings[tabId] = [...novelSettings[tabId], newSettings[tabId]];
        newSettings[tabId] = "";
    }

    function handleSignOut() {
        authService.signOut().then((resp) => {
            if (resp.toast) {
                addToast({
                    type: resp.toast.code === "SUCCESS" ? "success" : "error",
                    message: resp.toast.message,
                });
            }
            if (resp.redirectUrl) {
                goto(resp.redirectUrl);
            }
        });
    }

    onMount(() => {
        tabs.forEach((tab) => {
            novelSettings[tab.id] = [];
            newSettings[tab.id] = "";
            newSettingLoading[tab.id] = false;
        });
    });
</script>

<svelte:head>
    <title>蘑菇🍄 AI小说</title>
    <meta name="description" content="开启您的创作之旅" />
</svelte:head>

<div class="max-w-4xl mx-auto px-1">
    <Sticky class="mt-6 mb-4 flex flex-row item-center justify-between">
        {#snippet children(isSticky: boolean)}
            <h1 class="text-xl {isSticky ? 'invisible' : ''}">
                蘑菇🍄 AI 小说创作平台
                <Button href="/admin/activation-codes" variant="ghost">~</Button
                >
            </h1>
            <div class="flex flex-row">
                <div
                    class="flex item-center h-5 text-white {suggestion
                        ? 'block'
                        : 'hidden'}"
                >
                    <button
                        class="px-3 text-xs bg-red-600 hover:bg-red-700"
                        onclick={handleKeyEsc}>x</button
                    >
                    <button
                        class="px-3 text-xs bg-green-600 hover:bg-green-700"
                        onclick={handleKeyTab}>✓</button
                    >
                </div>
                <Loading class={isSuggesting ? "block" : "hidden"}></Loading>
                <div class="w-2"></div>
                <div class={isSticky ? "invisible" : ""}>
                    {#if authService.isActivated()}
                        <Button
                            variant="danger"
                            size="sm"
                            onclick={handleSignOut}
                            >退出
                        </Button>
                    {:else}
                        <Button variant="ghost" size="sm" href="/auth/signup"
                            >注册</Button
                        >
                        <Button size="sm" href="/login">登录</Button>
                    {/if}
                </div>
            </div>
        {/snippet}
    </Sticky>
    <AutoHeightTextarea
        class="p-2 border min-h-[20rem]"
        placeholder="小说正文"
        bind:value={text}
        {suggestion}
        {autofocus}
        {selectrange}
        on:input={handleInput}
        on:selectionchange={handleSelectionchage}
        on:blur={(e) => {
            autofocus = false;
        }}
        {shortcuts}
    ></AutoHeightTextarea>

    <Tabs
        variant="underline"
        fullWidth
        class="mt-2 mb-8"
        vertical={true}
        {tabs}
    >
        {#snippet children(tab: any)}
            {#if (novelSettings[tab.id]?.length || 0) === 0}
                <EmptyState title="暂无{tab.label}"></EmptyState>
            {:else}
                {#each novelSettings[tab.id] as item, index}
                    <div class="flex flex-row item-start my-2">
                        <AutoHeightTextarea
                            flex="w-full"
                            class="p-2 border"
                            placeholder="{tab.label}信息"
                            bind:value={novelSettings[tab.id][index]}
                        ></AutoHeightTextarea>
                        <Button
                            variant="ghost"
                            size="sm"
                            class="h-10"
                            onclick={() => {
                                novelSettings[tab.id].splice(index, 1);
                            }}><Minus></Minus></Button
                        >
                    </div>
                {/each}
            {/if}
            <AutoHeightTextarea
                class="p-2 border"
                placeholder="添加一个{tab.label}"
                bind:value={newSettings[tab.id]}
            ></AutoHeightTextarea>
            <div class="flex mt-2">
                <AutoGenerateButton
                    text="一键生成"
                    loading={newSettingLoading[tab.id]}
                    disabled={newSettings[tab.id].trim().length == 0}
                    on:click={() => handleAutoAiGen(tab)}
                ></AutoGenerateButton>
                <div class="flex-1"></div>
                <Button onclick={() => handleAddSetting(tab.id)}
                    ><Plus></Plus>添加一个{tab.label}</Button
                >
            </div>
        {/snippet}
    </Tabs>
</div>
