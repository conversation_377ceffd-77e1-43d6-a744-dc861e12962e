
import { initializeFirebaseAdmin } from '$lib/firebase/firebase-admin';
import type { DecodedIdToken } from 'firebase-admin/auth';
import { type Handle } from '@sveltejs/kit';
import { dev } from '$app/environment';
import { env } from '$env/dynamic/private';

/**
 * SvelteKit server hook that runs for every request on the server.
 * This hook is responsible for decoding the session cookie and populating `event.locals.user`.
 *
 * @see https://kit.svelte.dev/docs/hooks#server-hooks
 */
export const handle: Handle = async ({ event, resolve }) => {
	if (
		event.url.pathname.startsWith(
			'/.well-known'
		)
	) {
		return new Response(null, { status: 204 }); // Return empty response with 204 No Content
	}

	// --- Session Cookie Verification ---
	const sessionCookie = event.cookies.get('__session');

	// Default user to null.
	event.locals.user = null;

	if (sessionCookie) {
		try {
			let user: DecodedIdToken;
			if (dev) {
				const cookie = event.request.headers.get('cookie') || `__session=${sessionCookie}`;

				const resp = await fetch(env.DOMAIN + "/api/user/sessionIn", {
					method: "GET",
					headers: {
						"Cookie": cookie
					},
				});
				const data = await resp.json();
				if (resp.ok) {
					event.locals.user = data.user;
					user = data.user as DecodedIdToken;
				} else {
					throw new Error('Error verifying session cookie:' + data.message);
				}
			} else {
				// --- Firebase Admin Initialization ---
				const { fireauth } = initializeFirebaseAdmin();

				// If a session cookie exists, verify it.
				const decodedClaims = await fireauth.verifySessionCookie(sessionCookie, true);
				// If successful, the user's data is attached to the request event.
				user = decodedClaims as DecodedIdToken;
			}
			if (user) {
				console.log("get user", user);

				const admin = env.ADMIN_EMAIL === user.email;
				user.admin = admin;
				event.locals.user = user;
			}
		} catch (error) {
			// If verification fails, the user remains null, as if they were logged out.
			// You might want to add logging here for debugging purposes.
			console.error('Failed to verify session cookie:', error);
		}
	}

	// --- Request Resolution ---
	// Continue processing the request, which now has the user's identity (or lack thereof) established.
	return resolve(event);
};
