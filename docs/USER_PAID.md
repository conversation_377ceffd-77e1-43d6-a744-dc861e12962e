# 用户付费相关

## 08.19.2025

不搞那么多了, 也不搞什么订阅什么计划了, 现在赶紧把第一版(MVP)做出来.

首先, 激活码就是激活码, 不关联什么计划什么订阅了, 激活码的首要作用是防作弊, 其次都是其次.

激活码分两种: 
1. bonus, 一次性额度, 不会重置, 用完为止.
2. plan, 每月重置的额度, 会过期.

推广使用激活码, 由运营人员手动发放到用户指定的账户里, 这个激活码的额度是一次性的, 不会在月底重置, 因此需要设计一个奖励的额度类型, 并在用户表中新增配额池字段, 用于存储用户的额度.

OK, 现在只有激活码了, 让我们先把激活的数据结构设计出来.

```ts
{
    // 激活代码, 最高 20 位, 含大小写和数字, 可手动填写
    "code": "1111-2222-3333-4444-5555",
    // 激活码的额度
    "amount": {
        agent: 30,
        completion: 200
    },
    // 额度类型, bonus: 奖励, plan: 订阅计划
    "type": "bonus",
     // 是否已激活
    "isActive": false,
    // 有效时限, 单位 月
    "validity": 12, // 12个月
    // 创建时间
    "createdAt": 0,
    // 过期时间, 单位 月, 默认 1 个月未使用则过期, 无法使用
    "expireAt": 1,
    // 激活时间
    "activatedAt": null,
    // 激活邮箱
    "activatedEmail": null,
    // 激活码备注
    "remark": null
}
```

bonus: 奖励, 一次性额度, 不会重置.
plan: 订阅计划, 每月重置, 会过期.

validity: 激活码有效期, 单位月, 从激活时间开始计算, 为 0 表示不限制.
expireAt: 激活码过期时间, 默认 1 个月未使用则过期, 无法使用.

以上`code`, `amount`, `type`, `validity`, `expireAt`, `remark` 参数, 均可由运营人员设置.

OK, 激活码数据结构设计完成, 现在需要为用户表添加配额池字段.

```ts
{
    "quota_pool": [
        {"id": "1111-2222-3333-4444-5555", "type": "bonus", "amount": {
                agent: 100,
                completion: 500
            },
            "used": {
                agent: 10,
                completion: 100
            }, 
            "created_at": "2025-08-18T12:04:19.972Z"},
        {"id": "2222-3333-4444-5555-6666", "type": "plan", "amount": {
            agent: 30,
            completion: 200
        }, 
        "used": {
            agent: 0,
            completion: 0
        }, 
        "created_at": "2025-08-18T12:04:19.972Z"}
    ],
}
```

OK, 现在用户表设计完成, 还剩一个使用记录表了.

```ts
{
    "email": "<EMAIL>",
    "used": 1,
    "model": "chat", // 自动提示, 对话, 代理, 人物生成, 大纲生成等
    // 来源
    "quotaId": "1111-2222-3333-4444-5555", // quota_pool.id
    // token 使用情况, 以 openai 为例
    "token_usage": [
        ${OpenAI.Completions.CompletionUsage}
    ],
    "created_at": "2025-08-18 10:00:00"
}
```

## 08.18.2025

目前有两种方式用户配额.

1. 用户使用激活码开启订阅等级
  - 送的激活码对应 level 0, 免费, 额度较少, 30次/月, 自动提示 200 次
  - 可以通过一些付费渠道, 比如微信聊天购买, 但未设计对应等级, 需要业务跑通后再详细设计
2. 用户通过自媒体推广获得配额
  - 通过小红书, 抖音等自媒体渠道推广, 获得一定量的额度, 这个额度不是订阅的, 是一次性消耗的. 因此如何设计用户的配额体系, 是一个需要考虑的问题. 比如用户推广获得的额度, 直接打到对方账户里去还是发兑换码? 使用时, 需要优先使用推广获得的额度还是不考虑? 等等.

解决方案:

用户推广获得的额度计为奖励, 优先使用免费计划里的额度, 然后使用赠送的额度(即奖励). 奖励直接打到对方的账户里. 对于订阅计划, 每月标准时间 0 点重置额度, 使用 github action 操作.

奖励类型的配额, 由运营人员手动添加到指定帐户.

奖励依然使用激活码的形式发放和添加, 激活码新增一个类型为“奖励”.

激活码数据结构

```json
{
    // 激活代码
    "code": "1111-2222-3333-4444-5555",
    // 0: 每月额度 30 次
    "level": 0,
    // 激活码的额度
    "amount": 30,
    // 额度类型, bonus: 奖励, plan: 订阅计划
    "type": "bonus",
     // 是否已激活
    "isActive": true,
    // 有效时限
    "validity": 12, // 12个月
    // 创建时间
    "createdAt": 0,
    // 过期时间, 默认 1 年未使用则过期, 无法使用
    "expireAt": 0,
    // 激活时间
    "activatedAt": null,
    // 激活邮箱
    "activatedEmail": null
}
```

用户表中新增配额字段

quota_pool: 配额可用池
 - id: 配额 ID, 即激活码
 - type: 配额类型, bonus 表示奖励所得, plan 表示订阅计划
 - amount: 额度数量, 当前计划(等级)的配额, 固定值, 即使后续所属计划的配额发生改变, 此值也不会改变
 - used: 已使用数量, 在过期之前, 每月重置为 0, 类型为奖励的配额不在其中
 - created_at: 创建时间

```json
{
    "quota_pool": [
        {"id": "1111-2222-3333-4444-5555", "type": "bonus", "amount": 100, "used": 0, "created_at": "2025-08-18T12:04:19.972Z"},
        {"id": "2222-3333-4444-5555-6666", "type": "plan", "amount": 30, "used": 0, "created_at": "2025-08-18T12:04:19.972Z"}
    ],
}
```

新增用户配额使用量表

```json
{
    "email": "<EMAIL>",
    "used": 1,
    "model": "chat", // 自动提示, 对话, 代理, 人物生成, 大纲生成等
    // 来源
    "quotaId": "1111-2222-3333-4444-5555", // quota_pool.id
    // token 使用情况, 以 openai 为例
    "token_usage": {
        "openai": {
            "total": 1,
            "detail": [
                {
                    "model": "gpt-3.5-turbo",
                    "prompt": 100,
                    "completion": 200
                }
            ]
        }
    },
    "created_at": "2025-08-18 10:00:00"
}
```