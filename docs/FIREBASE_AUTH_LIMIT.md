## 限额

Firebase Authentication 限额: <https://firebase.google.com/docs/auth/limits>

### 每日无付款方式用量限额
| 用量 | 无付款方式限额 |
| --- | --- |
| 第1 层级的日活跃用户数 [1]| 每天3000 个 |
| 第2 层级的日活跃用户数 | 每天2 个 |

### 账号创建和删除限额
| 操作 | 限制 |
| --- | --- |
| 创建新账号 | 每个IP 地址每小时100 个账号 |
| 删除账号 | 每秒10 个账号 |
| 批量账号删除 | 每秒1 个请求 |
| 账号配置更新 | 每秒10 个请求 |

### 账号限制
| 账号类型 | 限制 |
| --- | --- |
| 匿名用户账号数 | 1 亿 |
| 注册用户账号数 | 无限额 |

### 电子邮件发送限额
| 操作 | Spark 方案的限额 | Blaze 方案的限额 |
| --- | --- | --- |
| 地址验证电子邮件 | 1000 封电子邮件/天 | 100,000 封电子邮件/天 |
| 地址更改电子邮件 | 1000 封电子邮件/天 | 10,000 封电子邮件/天 |
| 密码重置电子邮件 | 150 封电子邮件/天 | 10,000 封电子邮件/天 |
| “电子邮件链接登录”电子邮件 | 5 封电子邮件/天 | 25,000 封电子邮件/天 |

### 电子邮件链接生成限制
| 操作 | Spark 方案的限额 | Blaze 方案的限额 |
| --- | --- | --- |
| 地址验证链接 | 1 万个链接/天 | 100 万个链接/天 |
| 密码重置链接 | 1500 个链接/天 | 10 万个链接/天 |
| 登录链接 | 2 万个链接/天 | 25 万个链接/天 |

### 电话号码登录限制
| 操作 | 限制 |
| --- | --- |
| 用户登录 | 1600/分钟，以及价格页面中指定的价格和限制 |
| 验证码短信 | 仅限随用随付(Blaze) 方案。 <br> Firebase Authentication：3000 条发送的短信/天（上限） <br> Firebase Authentication with Identity Platform：无限制 |
| 验证请求 | 150 个请求/IP 地址/小时 |

### 验证短信发送限额
| 操作 | 限制 |
| --- | --- |
| 验证短信已发送。 | 每分钟发送1,000 条 |
| 每个IP 地址发送的验证短信限额 | 每分钟发送50 条，每小时发送500 条 |

### Identity Toolkit API 限额
| 操作 | 限制 |
| --- | --- |
| 每个服务账号的操作数 | 每秒500 个请求 |
| 每个项目的操作数 | 每秒1000 个请求，每天1000 万个请求 |
| 每个项目的账号上传次数* | 每分钟3600 次上传 |
| 每个项目的账号下载次数* | 每分钟21,000 个请求 |
| 每个项目的UserInfo 查询次数* | 每分钟900 个请求 |
| 每个项目的配置更新次数* | 每分钟300 个请求 |
| 每个项目和用户的配置更新次数* | 每分钟300 个请求 |
| 每个项目的账号批量删除次数* | 每分钟3000 个请求 |
| 每个项目的自定义令牌登录次数 | 每分钟45,000 次登录 |
| 每个IP 地址的 createAuthURI 调用次数 | 每小时120 个请求 |
| 每个项目的屏蔽函数调用次数 | 2000 个请求/分钟 |
| 每个项目的 GetAccountInfo 请求个数* | 500,000 个请求/分钟 |
*仅限管理员操作。

### Token Service API 限额
| 操作 | 限制 |
| --- | --- |
| 每个项目的令牌交换次数 | 每分钟18,000 次交换 |
