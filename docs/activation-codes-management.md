# 激活码管理系统文档

## 概述

激活码管理系统是一个现代化的 SPA 页面，用于管理蘑菇🍄 AI小说平台的激活码。系统支持两种类型的激活码：奖励额度（bonus）和订阅计划（plan），提供完整的 CRUD 操作和统计功能。

## 功能特性

### 🎯 核心功能
- ✅ 激活码创建、编辑、删除
- ✅ 激活码列表查看和搜索
- ✅ 激活码状态管理
- ✅ 统计信息展示
- ✅ 数据导出功能
- ✅ 响应式设计

### 🎨 用户体验
- ✅ 现代化 UI 设计，符合年轻人审美
- ✅ 渐变色彩搭配
- ✅ 流畅的动画效果
- ✅ 直观的操作界面
- ✅ 实时状态反馈

### 🔒 安全性
- ✅ 管理员权限验证
- ✅ Firebase 身份认证
- ✅ API 接口保护
- ✅ 输入数据验证

## 激活码类型

### 1. 奖励额度 (bonus)
- **特点**: 一次性额度，不会重置，用完为止
- **用途**: 推广活动、用户奖励
- **图标**: 🎁 礼物图标
- **颜色**: 紫色主题

### 2. 订阅计划 (plan)
- **特点**: 每月重置的额度，会过期
- **用途**: 订阅服务、定期计划
- **图标**: 📅 日历图标
- **颜色**: 蓝色主题

## 数据结构

### 激活码对象 (ActivationCode)

```typescript
interface ActivationCode {
  // 激活代码，最高 20 位，含大小写和数字
  code: string;
  
  // 激活码的额度
  amount: {
    agent: number;        // AI 代理额度
    completion: number;   // 文本补全额度
  };
  
  // 额度类型
  type: 'bonus' | 'plan';
  
  // 是否已激活
  isActive: boolean;
  
  // 有效时限，单位月，从激活时间开始计算，为 0 表示不限制
  validity: number;
  
  // 创建时间（时间戳）
  createdAt: number;
  
  // 过期时间，单位月，默认 1 个月未使用则过期
  expireAt: number;
  
  // 激活时间（时间戳，null 表示未激活）
  activatedAt: number | null;
  
  // 激活邮箱
  activatedEmail: string | null;
  
  // 激活码备注
  remark: string | null;
  
  // 当前状态（计算属性）
  status?: 'active' | 'used' | 'expired';
  
  // 剩余有效天数（计算属性）
  remainingDays?: number;
}
```

### 状态计算逻辑

```typescript
// 状态计算
function calculateStatus(code: ActivationCode): ActivationCodeStatus {
  // 1. 如果已被激活（有激活邮箱）-> 已使用
  if (code.activatedEmail) return 'used';
  
  // 2. 检查是否过期
  const now = Date.now();
  const expireTime = code.createdAt + (code.expireAt * 30 * 24 * 60 * 60 * 1000);
  if (now > expireTime) return 'expired';
  
  // 3. 否则为可用状态
  return 'active';
}
```

## 技术架构

### 前端技术栈
- **框架**: SvelteKit 5 (Runes 模式)
- **语言**: TypeScript
- **样式**: TailwindCSS
- **图标**: Lucide Svelte
- **状态管理**: Svelte 5 Runes

### 后端技术栈
- **运行时**: Node.js
- **框架**: SvelteKit API Routes
- **数据库**: Firebase Firestore
- **认证**: Firebase Auth
- **部署**: Vercel

### 文件结构

```
src/
├── lib/
│   ├── types/
│   │   └── activation-code.types.ts     # 类型定义
│   ├── utils/
│   │   └── activation-code.utils.ts     # 工具函数
│   └── components/
│       ├── ui/                          # UI 组件
│       └── admin/
│           └── AdminGuard.svelte        # 管理员权限守卫
├── routes/
│   ├── admin/
│   │   └── activation-codes/
│   │       ├── +page.svelte            # 主页面
│   │       └── +page.server.ts         # 服务器端加载器
│   └── api/
│       └── admin/
│           └── activation-codes/
│               ├── +server.ts          # 主 API
│               ├── [id]/
│               │   └── +server.ts      # 单个激活码 API
│               └── stats/
│                   └── +server.ts      # 统计 API
└── docs/
    └── activation-codes-management.md  # 本文档
```

## API 接口

### 1. 获取激活码列表
```http
GET /api/admin/activation-codes
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `search`: 搜索关键词
- `type`: 类型筛选 ('bonus' | 'plan')
- `status`: 状态筛选 ('active' | 'used' | 'expired')
- `sortBy`: 排序字段 ('createdAt' | 'activatedAt' | 'expireAt' | 'validity')
- `sortOrder`: 排序方向 ('asc' | 'desc')

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [/* ActivationCode[] */],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 2. 创建激活码
```http
POST /api/admin/activation-codes
```

**请求体**:
```json
{
  "code": "ABCD-1234-EFGH-5678",  // 可选，不提供则自动生成
  "amount": {
    "agent": 30,
    "completion": 200
  },
  "type": "bonus",
  "validity": 12,
  "expireAt": 1,
  "remark": "推广活动激活码"
}
```

### 3. 更新激活码
```http
PATCH /api/admin/activation-codes/{id}
```

### 4. 删除激活码
```http
DELETE /api/admin/activation-codes/{id}
```

### 5. 获取统计信息
```http
GET /api/admin/activation-codes/stats
```

## 使用指南

### 管理员访问
1. 使用管理员邮箱登录系统
2. 访问 `/admin/activation-codes` 页面
3. 系统会自动验证管理员权限

### 创建激活码
1. 点击右上角"创建激活码"按钮
2. 选择激活码类型（奖励/订阅计划）
3. 设置额度信息
4. 配置有效期和过期时间
5. 添加备注信息（可选）
6. 点击"创建激活码"完成

### 管理激活码
- **查看详情**: 点击眼睛图标查看完整信息
- **编辑**: 点击编辑图标修改激活码信息
- **删除**: 点击删除图标移除激活码
- **搜索**: 使用搜索框查找特定激活码
- **筛选**: 使用类型和状态筛选器
- **导出**: 点击导出按钮下载激活码列表

### 状态说明
- 🟢 **可用**: 激活码未被使用且未过期
- 🟠 **已使用**: 激活码已被用户激活
- 🔴 **已过期**: 激活码超过过期时间未使用

## 安全考虑

### 权限控制
- 只有配置的管理员邮箱才能访问管理页面
- 所有 API 接口都需要有效的 Firebase ID Token
- 服务器端双重验证管理员身份

### 数据保护
- 激活码存储在 Firebase Firestore 中
- 已使用的激活码不能被修改或删除
- 所有操作都有详细的日志记录

### 输入验证
- 前端表单验证
- 后端 API 参数验证
- 激活码格式验证

## 性能优化

### 前端优化
- 组件懒加载
- 图片优化
- CSS 压缩
- JavaScript 代码分割

### 后端优化
- Firestore 查询优化
- 分页加载
- 缓存策略
- API 响应压缩

## 部署说明

### 环境变量
```env
ADMIN_EMAIL=<EMAIL>
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY=your-private-key
FIREBASE_CLIENT_EMAIL=your-client-email
```

### 部署步骤
1. 配置 Firebase 项目
2. 设置环境变量
3. 部署到 Vercel
4. 配置域名和 SSL

## 维护指南

### 定期任务
- 清理过期激活码
- 监控使用统计
- 备份数据库
- 更新依赖包

### 故障排除
- 检查 Firebase 连接
- 验证管理员权限
- 查看服务器日志
- 测试 API 接口

## 更新日志

### v1.0.0 (2024-08-19)
- ✅ 初始版本发布
- ✅ 完整的激活码管理功能
- ✅ 现代化 UI 设计
- ✅ 管理员权限控制
- ✅ 统计信息展示
- ✅ 数据导出功能

## 技术支持

如有问题或建议，请联系开发团队。

---

**开发者**: Augment Agent  
**最后更新**: 2024-08-19  
**版本**: v1.0.0
