# 激活码管理系统 - 快速开始

## 🚀 快速概览

激活码管理系统是一个现代化的管理界面，用于创建和管理蘑菇🍄 AI小说平台的激活码。

### 激活码类型

| 类型 | 图标 | 说明 | 用途 |
|------|------|------|------|
| **奖励额度** (bonus) | 🎁 | 一次性额度，不会重置 | 推广活动、用户奖励 |
| **订阅计划** (plan) | 📅 | 每月重置，会过期 | 订阅服务、定期计划 |

## 🔑 访问方式

1. 使用管理员邮箱登录系统
2. 访问 `/admin/activation-codes` 页面
3. 系统自动验证管理员权限

## ✨ 主要功能

### 📊 统计面板
- 总激活码数量
- 可用/已使用/已过期统计
- 按类型分组统计
- 实时数据更新

### 🔍 搜索和筛选
- 按激活码搜索
- 按备注搜索
- 类型筛选（奖励/订阅计划）
- 状态筛选（可用/已使用/已过期）

### ➕ 创建激活码

1. 点击右上角"创建激活码"按钮
2. 填写表单：
   - **激活码**: 留空自动生成，或手动输入
   - **类型**: 选择奖励额度或订阅计划
   - **额度**: 设置 AI 代理和文本补全额度
   - **有效时限**: 从激活时开始计算（月）
   - **过期时间**: 未使用的过期时间（月）
   - **备注**: 可选的说明信息
3. 点击"创建激活码"完成

### ✏️ 管理激活码

| 操作 | 图标 | 说明 |
|------|------|------|
| 查看详情 | 👁️ | 查看激活码完整信息 |
| 编辑 | ✏️ | 修改激活码信息（仅可用状态） |
| 删除 | 🗑️ | 删除激活码（不能删除已使用） |

### 📤 导出功能
- 点击"导出"按钮
- 下载当前筛选结果的激活码列表
- 格式：制表符分隔的文本文件

## 🎨 界面特色

### 现代化设计
- 渐变色彩搭配
- 流畅动画效果
- 响应式布局
- 直观操作界面

### 状态指示
- 🟢 **可用**: 绿色，激活码可正常使用
- 🟠 **已使用**: 橙色，已被用户激活
- 🔴 **已过期**: 红色，超过过期时间

### 智能提示
- 即将过期提醒
- 剩余天数显示
- 操作结果反馈
- 错误信息提示

## 🔒 安全特性

- ✅ 管理员权限验证
- ✅ Firebase 身份认证
- ✅ API 接口保护
- ✅ 输入数据验证
- ✅ 已使用激活码保护

## 📱 响应式支持

- 桌面端优化体验
- 平板设备适配
- 移动端友好界面
- 触摸操作支持

## 🛠️ 故障排除

### 常见问题

**Q: 无法访问管理页面？**
A: 检查是否使用管理员邮箱登录，确认邮箱已验证。

**Q: 创建激活码失败？**
A: 检查网络连接，确认所有必填字段已填写。

**Q: 无法编辑激活码？**
A: 只有"可用"状态的激活码可以编辑，已使用或已过期的不能修改。

**Q: 删除按钮是灰色的？**
A: 已使用的激活码不能删除，这是为了保护数据完整性。

### 联系支持
如遇到其他问题，请联系技术支持团队。

## 🎯 最佳实践

### 创建激活码
- 为不同活动使用不同的备注
- 合理设置过期时间
- 根据用途选择正确的类型

### 管理建议
- 定期清理过期激活码
- 监控使用统计
- 及时处理即将过期的激活码

### 安全建议
- 不要在公共场所操作
- 定期更换管理员密码
- 谨慎分享激活码

---

**💡 提示**: 这个系统设计简洁易用，符合现代年轻人的使用习惯。如有任何疑问，请参考完整文档或联系技术支持。
