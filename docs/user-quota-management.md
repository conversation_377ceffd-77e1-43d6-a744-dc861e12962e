# 用户配额管理系统

## 🚀 项目概述

用户配额管理系统是一个现代化的管理界面，用于管理蘑菇🍄 AI小说平台的用户配额和Token使用情况。系统提供了完整的用户配额查看、管理、统计和操作功能。

### 主要特性

- 📊 **实时统计** - 系统整体用户和配额使用统计
- 👥 **用户管理** - 用户列表查看、搜索和筛选
- 💰 **配额管理** - 用户配额查看、调整和批量操作
- 📈 **使用分析** - Token使用记录和趋势分析
- 🔧 **批量操作** - 支持批量配额调整和管理
- 📱 **响应式设计** - 适配桌面和移动设备

## 🏗️ 系统架构

### 技术栈

- **前端**: SvelteKit + TypeScript + TailwindCSS
- **后端**: SvelteKit API Routes + Firebase Admin SDK
- **数据库**: Firestore
- **认证**: Firebase Authentication
- **UI组件**: 自定义组件库

### 数据结构

#### 用户配额数据
```typescript
interface UserQuotaInfo {
  uid: string;
  email: string;
  quotaPool: UserQuotaItem[];
  totalQuota: {
    agent: { total: number; used: number; remaining: number };
    completion: { total: number; used: number; remaining: number };
  };
}
```

#### Token使用记录
```typescript
interface TokenUsageRecord {
  id: string;
  uid: string;
  email: string;
  model: QuotaType;
  tokenUsage: CompletionUsage[];
  createdAt: number;
}
```

## 📁 文件结构

```
src/
├── routes/
│   ├── admin/user-quota/+page.svelte          # 主页面
│   └── api/admin/
│       ├── users/+server.ts                   # 用户列表API
│       ├── users/[uid]/+server.ts             # 用户详情API
│       ├── users/[uid]/quota/+server.ts       # 配额操作API
│       ├── users/batch-quota/+server.ts       # 批量操作API
│       └── stats/+server.ts                   # 系统统计API
├── lib/
│   ├── components/admin/
│   │   ├── UserDetailModal.svelte             # 用户详情模态框
│   │   └── BatchOperationModal.svelte         # 批量操作模态框
│   └── types/
│       └── user-quota.types.ts                # 类型定义
└── docs/
    └── user-quota-management.md               # 项目文档
```

## 🔌 API 接口

### 1. 获取用户列表
```http
GET /api/admin/users
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `search`: 搜索关键词
- `sortBy`: 排序字段 (email|createdAt|lastUsedAt|totalUsage)
- `sortOrder`: 排序方向 (asc|desc)
- `hasQuota`: 是否有配额 (true|false)
- `isActive`: 是否活跃 (true|false)

**响应:**
```json
{
  "success": true,
  "data": {
    "items": [UserQuotaInfo],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 2. 获取用户详情
```http
GET /api/admin/users/{uid}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "userInfo": UserQuotaInfo,
    "usageStats": UserUsageStats,
    "recentRecords": TokenUsageRecord[]
  }
}
```

### 3. 配额操作
```http
POST /api/admin/users/{uid}/quota
```

**请求体:**
```json
{
  "operation": "add|subtract|reset|expire",
  "quotaType": "agent|completion",
  "amount": 100,
  "reason": "操作原因"
}
```

### 4. 批量配额操作
```http
POST /api/admin/users/batch-quota
```

**请求体:**
```json
{
  "userIds": ["uid1", "uid2"],
  "operation": {
    "operation": "add",
    "quotaType": "agent",
    "amount": 100,
    "reason": "批量增加配额"
  }
}
```

### 5. 系统统计
```http
GET /api/admin/stats
```

**响应:**
```json
{
  "success": true,
  "data": {
    "totalUsers": 1000,
    "activeUsers": 500,
    "totalQuotaDistributed": { "agent": 10000, "completion": 20000 },
    "totalQuotaUsed": { "agent": 5000, "completion": 8000 },
    "totalTokensConsumed": 1000000,
    "averageTokensPerUser": 1000,
    "topUsers": [...]
  }
}
```

## 🎨 UI 组件

### 主页面功能
- **统计卡片**: 显示系统整体统计信息
- **搜索筛选**: 支持多条件搜索和筛选
- **用户列表**: 分页显示用户信息和配额状态
- **批量操作**: 选择多个用户进行批量配额操作

### 用户详情模态框
- **基本信息**: 用户账户信息和状态
- **配额详情**: 配额池详情和使用情况
- **使用统计**: Token使用统计和趋势
- **使用记录**: 最近的使用记录列表
- **配额操作**: 单个用户的配额调整

### 批量操作模态框
- **操作选择**: 增加、减少、重置、过期配额
- **参数设置**: 配额类型、数量、操作原因
- **操作预览**: 显示将要执行的操作详情

## 🔐 权限控制

系统使用Firebase Authentication进行身份验证，只有管理员邮箱（通过环境变量`ADMIN_EMAIL`配置）才能访问管理功能。

### 权限验证流程
1. 前端获取用户的Firebase ID Token
2. 后端验证Token的有效性
3. 检查用户邮箱是否为管理员邮箱
4. 验证通过后执行相应操作

## 📊 数据聚合

系统使用Firebase Admin SDK进行数据聚合查询：

### 用户配额统计
- 从用户的`customClaims.quota_pool`中聚合配额信息
- 计算总配额、已使用配额和剩余配额

### Token使用统计
- 从`user_token_usages`集合中聚合使用记录
- 按日期、用户、模型类型进行分组统计
- 计算Token消费趋势和使用模式

### 系统整体统计
- 聚合所有用户的配额分发和使用情况
- 计算活跃用户数（最近30天有使用记录）
- 统计Top用户和平均使用量

## 🚀 部署指南

### 环境变量配置
```env
ADMIN_EMAIL=<EMAIL>
GOOGLE_FIREBASE_ADMIN_SERVICE_ACCOUNT={"type":"service_account",...}
```

### 部署步骤
1. 确保Firebase项目已配置
2. 设置环境变量
3. 部署SvelteKit应用
4. 配置管理员权限

## 🎯 使用说明

### 访问系统
1. 使用管理员邮箱登录系统
2. 访问 `/admin/user-quota` 页面
3. 系统自动验证管理员权限

### 查看用户配额
1. 在用户列表中浏览所有用户
2. 使用搜索框查找特定用户
3. 点击用户行查看详细信息

### 管理用户配额
1. 点击用户详情查看配额状态
2. 使用配额操作功能调整配额
3. 查看使用统计和记录

### 批量操作
1. 选择多个用户（使用复选框）
2. 点击批量操作按钮
3. 选择操作类型和参数
4. 确认执行批量操作

## 🔧 开发指南

### 本地开发
```bash
npm install
npm run dev
```

### 类型检查
```bash
npm run check
```

### 构建生产版本
```bash
npm run build
```

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 📊 用户配额管理功能
- 🔧 批量操作支持
- 📈 使用统计和分析
- 📱 响应式设计

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 🔍 故障排除

### 常见问题

**Q: 无法访问管理页面？**
A: 检查是否使用管理员邮箱登录，确认`ADMIN_EMAIL`环境变量配置正确。

**Q: 用户列表加载缓慢？**
A: 系统会获取所有用户信息，用户数量较多时可能较慢。可以使用搜索功能缩小范围。

**Q: 批量操作失败？**
A: 检查选择的用户数量（最多100个），确认操作参数正确。

**Q: 统计数据不准确？**
A: 统计数据基于Firestore聚合查询，可能存在轻微延迟。

### 性能优化建议

1. **分页加载**: 使用合适的分页大小（建议20-50条）
2. **搜索优化**: 使用精确的搜索条件减少数据量
3. **缓存策略**: 考虑在前端缓存统计数据
4. **索引优化**: 为常用查询字段创建Firestore索引

## 📊 监控和日志

### 操作日志
系统会记录所有管理员操作：
- 单个用户配额操作记录在`admin_operation_logs`集合
- 批量操作记录在`admin_batch_operation_logs`集合

### 监控指标
- API响应时间
- 错误率
- 用户活跃度
- 配额使用趋势

## 🔮 未来规划

### 计划功能
- [ ] 配额使用预警
- [ ] 更详细的使用分析图表
- [ ] 配额模板管理
- [ ] 用户分组管理
- [ ] 导出功能增强
- [ ] 实时通知系统

### 技术改进
- [ ] 数据库查询优化
- [ ] 缓存机制
- [ ] 批量操作性能优化
- [ ] 移动端体验优化

## 📄 许可证

本项目采用 MIT 许可证。
