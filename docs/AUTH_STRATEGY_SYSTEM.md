# 认证策略系统

## 概述

本项目实现了一个灵活的认证策略系统，能够根据客户端网络环境自动选择最佳的认证方式。系统支持两种主要策略：

1. **Firebase SDK 直连** - 直接使用Firebase客户端SDK进行认证
2. **服务端代理** - 通过服务端代理Firebase REST API进行认证

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    UnifiedAuthService                       │
│                    (统一认证服务入口)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                AuthStrategyFactory                          │
│                (认证策略工厂)                                │
├─────────────────────┬───────────────────────────────────────┤
│  NetworkDetector    │  Strategy Selection Logic            │
│  (网络检测)         │  (策略选择逻辑)                       │
└─────────────────────┼───────────────────────────────────────┘
                      │
        ┌─────────────┴─────────────┐
        │                           │
┌───────▼──────┐            ┌──────▼──────┐
│ Firebase SDK │            │ Server Proxy│
│   Strategy   │            │  Strategy   │
│   (直连策略)  │            │ (代理策略)   │
└──────────────┘            └─────────────┘
```

## 核心组件

### 1. 认证策略接口 (IAuthStrategy)

所有认证策略都实现此接口，提供统一的认证方法：

```typescript
interface IAuthStrategy {
  signUp(email: string, password: string): Promise<AuthResponse>;
  signIn(email: string, password: string): Promise<AuthResponse>;
  signInWithGoogle(): Promise<AuthResponse>;
  signOut(): Promise<void>;
  // ... 其他认证方法
}
```

### 2. 网络检测服务 (NetworkDetectorService)

负责检测网络环境和Firebase服务的可访问性：

```typescript
interface NetworkStatus {
  isOnline: boolean;
  canAccessFirebase: boolean;
  canAccessGoogle: boolean;
  latency?: number;
  lastChecked: number;
}
```

### 3. 认证策略工厂 (AuthStrategyFactory)

根据网络状态自动选择和切换认证策略：

```typescript
class AuthStrategyFactory {
  createStrategy(strategy: AuthStrategy): IAuthStrategy;
  getRecommendedStrategy(networkStatus: NetworkStatus): AuthStrategy;
  switchStrategy(newStrategy: AuthStrategy): Promise<void>;
}
```

### 4. 统一认证服务 (UnifiedAuthService)

提供统一的认证接口，自动处理策略选择和切换：

```typescript
class UnifiedAuthService {
  async signUp(email: string, password: string): Promise<AuthResponse>;
  async signIn(email: string, password: string): Promise<AuthResponse>;
  async signInWithGoogle(): Promise<AuthResponse>;
  // ... 其他方法
}
```

## 策略选择逻辑

系统根据以下条件自动选择认证策略：

1. **网络连接状态**
   - 离线：使用缓存的策略
   - 在线：继续检测

2. **Firebase服务可访问性**
   - 可访问Firebase和Google服务：优先使用Firebase SDK
   - 无法访问：使用服务端代理

3. **用户配置**
   - 自动切换启用：根据网络状态自动选择
   - 自动切换禁用：使用用户指定的策略

## 使用方法

### 基本使用

```typescript
import { authService } from "$lib/services/auth.service";

// 登录 - 系统会自动选择最佳策略
const result = await authService.signIn(email, password);

// Google登录 - 如果当前策略不支持，会自动切换到Firebase SDK
const googleResult = await authService.signInWithGoogle();
```

### 手动策略控制

```typescript
import { AuthStrategy } from "$lib/services/auth-strategy.types";

// 手动切换到Firebase SDK
await authService.switchStrategy(AuthStrategy.FIREBASE_SDK);

// 手动切换到服务端代理
await authService.switchStrategy(AuthStrategy.SERVER_PROXY);

// 检查当前策略
const currentStrategy = authService.getCurrentStrategyType();
```

### 配置管理

```typescript
// 启用自动策略切换
authService.updateConfig({ autoSwitch: true });

// 设置网络检测间隔
authService.updateConfig({ checkInterval: 30000 });

// 获取当前配置
const config = authService.getConfig();
```

## UI组件

### AuthStrategySelector

提供用户友好的策略选择界面：

```svelte
<script>
  import { AuthStrategySelector } from "$lib/components/auth";
</script>

<!-- 完整模式 -->
<AuthStrategySelector showAdvanced={true} />

<!-- 紧凑模式 -->
<AuthStrategySelector compact={true} />
```

## 兼容性

新系统完全向后兼容，现有代码无需修改：

```typescript
// 原有代码继续工作
import { authService } from "$lib/services/auth.service";

const result = await authService.signIn(email, password);
const isAuth = authService.isAuthenticated();
```

## 测试

访问 `/auth-strategy-test` 页面进行系统测试：

- 网络状态检测
- 策略健康状态检查
- 手动策略切换
- 认证功能测试

## 配置选项

```typescript
interface AuthStrategyConfig {
  strategy: AuthStrategy;           // 默认策略
  fallbackStrategy?: AuthStrategy;  // 备用策略
  autoSwitch: boolean;             // 自动切换
  checkInterval: number;           // 检测间隔(ms)
  timeout: number;                 // 超时时间(ms)
}
```

## 故障排除

### 常见问题

1. **Google登录失败**
   - 检查Firebase配置是否正确
   - 确认网络可以访问Google服务
   - 尝试手动切换到Firebase SDK策略

2. **策略切换失败**
   - 检查目标策略是否可用
   - 查看浏览器控制台错误信息
   - 尝试刷新网络状态

3. **自动切换不工作**
   - 确认自动切换已启用
   - 检查网络检测服务是否正常
   - 查看网络状态检测结果

### 调试

启用详细日志：

```typescript
// 在浏览器控制台中
localStorage.setItem('auth-debug', 'true');
```

## 性能优化

1. **网络检测缓存** - 避免频繁的网络检测
2. **策略复用** - 相同策略实例复用
3. **懒加载** - 按需加载策略实现
4. **错误恢复** - 自动回退到可用策略

## 安全考虑

1. **Token管理** - 统一的Token刷新机制
2. **错误处理** - 安全的错误信息展示
3. **网络检测** - 使用安全的检测端点
4. **策略隔离** - 不同策略间的数据隔离
