{"name": "webnovel", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/vite": "^4.0.0", "@types/unidecode": "^1.1.0", "eslint": "^9.18.0", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6"}, "dependencies": {"@lucide/svelte": "^0.513.0", "dexie": "^4.0.11", "firebase": "^12.0.0", "firebase-admin": "^13.4.0", "fuse.js": "^7.1.0", "lucide-svelte": "^0.535.0", "moment": "^2.30.1", "nanoid": "^5.1.5", "node-fetch": "^3.3.2", "openai": "^5.11.0", "unidecode": "^1.1.0"}}