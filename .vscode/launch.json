{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Run svelte-kit",
            "runtimeExecutable": "svelte-kit",
            "cwd": "${workspaceFolder}",
            "args": []
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Program",
            "runtimeExecutable": "npm",
            "runtimeArgs": [
                "run",
                "dev" // Replace with the name of your npm script (e.g., "start", "dev")
            ],
            "skipFiles": [
                "<node_internals>/**"
            ]
        }
    ]
}